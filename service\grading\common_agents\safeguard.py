import concurrent.futures
import ast
from service.openai_utils.gpt_call import make_openai_langfuse
import os
from service.langfuse_client import langfuse
from service.context_pipeline import logger


def verify_feedback( graded_assignment, assignment_type):
    """
    Verifies the feedback provided for a graded assignment by ensuring its correctness and appropriateness.
    Uses multithreading to process each question efficiently.

    Parameters:
    - graded_assignment (list/dict/str): The graded assignment with feedback to be verified.
    - assignment_type (str): The type of assignment (e.g., essay, math, history, etc.).
    - use_vision (bool): Whether vision-based processing should be used for verification.
    - images (list): List of images associated with the assignment (if applicable).
    - description (list): Additional descriptions related to the assignment.

    Returns:
    - dict/list: The verified graded assignment with corrected feedback.
    """
    print("Verifying feedback:", graded_assignment)

    # if isinstance(graded_assignment, str):
    #     graded_assignment = ast.literal_eval(graded_assignment)
    # if isinstance(graded_assignment, list):
    #     pass
    # else:
    #     graded_assignment = [graded_assignment]

    temp = []

    # --- MULTI-THREADING SECTION BEGINS HERE ---
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Submit each question's processing as a separate task
        future_to_question = {
            executor.submit(sanitize_feedback_content,question): question
            for question in graded_assignment
        }

        # As each future completes, we collect the result
        for future in concurrent.futures.as_completed(future_to_question):
            question = future_to_question[future]
            try:
                response = future.result()
                print(response)
                temp.append(response)
            except Exception as exc:
                print(f"Error processing question {question}: {exc}")
    # --- MULTI-THREADING SECTION ENDS HERE ---

    # Merge the feedback into the graded_assignment
    print("working till here")
    logger.info(f"Processing assignment type: {assignment_type}")
    logger.info(f"Number of items in temp: {len(temp)}")

    if 'essay' in assignment_type:
        logger.info("Processing essay assignment type")
        logger.info(f"Assigning feedback from temp[0]: {temp[0]['personalized_feedback']}")
        graded_assignment[0]['personalized_feedback']=temp[0]['personalized_feedback']
        logger.info("Successfully assigned personalized feedback to essay assignment")
    else:
        logger.info("Processing non-essay assignment type")
        for question in graded_assignment:
            index = question.index
            logger.info(f"Processing question with index: {index}")
            for item in temp:
                if int(index) == int(item['index']):
                    logger.info(f"Found matching index {index}, assigning feedback: {item['personalized_feedback']}")
                    question.personalized_feedback = item['personalized_feedback']
                    break
        logger.info("Successfully processed all questions for non-essay assignment")

    if 'essay' in assignment_type:
        logger.info("Converting essay assignment to single item format")
        graded_assignment=graded_assignment[0]
        logger.info(f"Final essay assignment: {graded_assignment}")
    print("PROCESSED ASSIGNMENT:", graded_assignment)
    return graded_assignment


def sanitize_feedback_content(assignment):
    """
    This function ensures personalized feedback for assignments adheres to content guidelines by removing:
    - Correct answers/solutions
    - Gender-specific pronouns
    - Offensive language
    Maintains original feedback intent while enforcing neutral language and solution-agnostic guidance.

    Parameters:
    - assignment (dict/list): Assignment data containing personalized_feedback to be sanitized
    - reading_level (str): Student's reading level (unused in current logic but kept for structure consistency)
    - images (list): List of images (for vision compatibility)
    - use_vision (bool): Whether to use image analysis
    - image_description (list): Image descriptions

    Returns:
    - dict/list: Updated assignment with sanitized feedback
    """
    print('sanitizing feedback content')
    if type(assignment)!=dict:
        print(type(assignment))

    messages = langfuse.get_prompt("safeguard", label="latest")
    complete_chat_prompt = messages.compile(
        assignment=assignment
    )

    tool = [{
        "type": "function",
        "function": {
            "name": "adjust_feedback",
            "description": "Sanitize feedback by removing prohibited content while preserving educational value",
            "parameters": {
                "type": "object",
                "properties": {
                    "personalized_feedback": {
                        "type": "string",
                        "description": "Cleaned feedback without answers, solutions, pronouns, or offensive language"
                    }
                },
                "required": ['personalized_feedback']
            }
        }
    }]


    response = make_openai_langfuse(complete_chat_prompt, tool, temperature=0.7)

    # Update assignment structure
    if isinstance(assignment, dict):
        assignment.personalized_feedback = response['personalized_feedback']
    else:
        print("ASSIGNMENT:",assignment)
    if hasattr(assignment,'index'):
        response['index']=assignment.index
    return assignment