
def spanish_vocab_example(grade): 
    """
    Returns a list of Spanish vocabulary words based on the specified grade level.
    Parameters:
    grade (str or int): The grade level for which to retrieve the Spanish vocabulary. 
                        Acceptable values include:
                        - "1st", "1", "first", "kindergarten"
                        - "2nd", "2", "second"
                        - "3rd", "3", "third"
                        - "4th", "4", "fourth"
                        - "5th", "5", "fifth"
                        - "6th", "6", "sixth", "7th", "7", "seventh", "8th", "8", "eighth"
                        - "9th", "9", "nineth", "10th", "10", "tenth", "11th", "11", "eleventh", "12th", "12", "twelfth", "HS"
    Returns:
    list: A list of Spanish vocabulary words corresponding to the specified grade level. 
          Returns an empty list if the grade level is not recognized.
    """
    grade = str(grade).lower()
    if grade in ["1st", "1", "first", "kindergarten"]:
        return spanish_vocab_g1
    elif grade in ["2nd", "2", "second"]:
        return spanish_vocab_g2
    elif grade in ["3rd", "3", "third"]:
        return spanish_vocab_g3
    elif grade in ["4th", "4", "fourth"]:
        return spanish_vocab_g4
    elif grade in ["5th", "5", "fifth"]:
        return spanish_vocab_g5
    elif grade in ["6th", "6", "sixth", "7th", "7", "seventh", "8th", "8", "eighth"]:
        return spanish_vocab_g6_8
    elif grade in ["9th", "9", "nineth", "10th", "10", "tenth", "11th", "11", "eleventh", "12th", "12", "twelfth", "HS"]:
        return spanish_vocab_g9_12
    else:
        return []
    
spanish_vocab_g6_8 = [
    "ecuación",
    "álgebra",
    "fracción",
    "decimal",
    "literatura",
    "resumir",
    "análisis",
    "narrativa",
    "antiguo",
    "revolución",
    "cultura",
    "proporción",
    "ángulo",
    "historia",
    "civilización",
    "ensayo",
    "perspectiva",
    "estructura",
    "diversidad",
    "economía",
    "democracia",
    "conclusión",
    "métrica",
    "geometría",
    "probabilidad",
    "multiplicar",
    "dividir",
    "significado",
    "contexto",
    "valor",
    "imperio",
    "innovación"
]
spanish_vocab_g9_12 = [
    "teorema",
    "diferenciación",
    "integración",
    "trigonometría",
    "análisis literario",
    "metáfora",
    "historiografía",
    "monarquía",
    "democrático",
    "reforma",
    "globalización",
    "sistema político",
    "estrategia militar",
    "filosofía",
    "renacimiento",
    "barroco",
    "romanticismo",
    "ilustración",
    "derivada",
    "logaritmo",
    "exponencial",
    "crónica",
    "análisis crítico",
    "contexto histórico",
    "cálculo",
    "estadística",
    "síntesis",
    "literatura mundial",
    "imperialismo",
    "colonización",
    "resistencia",
    "dialéctica"
]
spanish_vocab_g1 = [
    "lección",
    "cada",
    "resolver",
    "forma",
    "oración",
    "crecer",
    "vocabulario",
    "comparar",
    "gráfico",
    "ciencia"
]
spanish_vocab_g2 = [
    "investigación",
    "suma",
    "esencial",
    "patrón",
    "explicar",
    "debajo",
    "observar",
    "actividad",
    "estrategia",
    "parecido"
]
spanish_vocab_g3 = [
    "recurso",
    "describir",
    "detalle",
    "imaginar",
    "grande",
    "completo",
    "modelo",
    "tema",
    "natural",
    "independiente"
]
spanish_vocab_g4 = [
    "predecir",
    "símbolo",
    "tecnología",
    "estimación",
    "análisis",
    "multiplicar",
    "dividir",
    "fracción",
    "decimal",
    "proporción"
]
spanish_vocab_g5 = [
    "ángulo",
    "civilización",
    "estructura",
    "diversidad",
    "economía",
    "democracia",
    "conclusión",
    "métrica",
    "geometría",
    "probabilidad"
]
