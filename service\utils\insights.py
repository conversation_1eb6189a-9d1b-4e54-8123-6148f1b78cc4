import requests

url = "https://us-central1-bubbly-granite-412822.cloudfunctions.net/class_insights_staging"

def update_insights(class_id,student_id,assignment_name,latest_assignment):
    """
    Sends updated insights about a student's assignment to the class insights API.

    Parameters:
    - class_id (str): The unique identifier of the class.
    - student_id (str): The unique identifier of the student.
    - assignment_name (str): The name of the assignment.
    - latest_assignment (dict): The latest assignment data, containing key insights and scores.

    Returns:
    - None: The function prints the response from the API call.
    """
    
    print("updating the insights for the student")
    headers = {
        "Content-Type": "application/json",
        # "Authorization": "Bearer YOUR_TOKEN_HERE"
    }
    
    payload = {
        "class_id": class_id,
        "student_ids": [student_id],
        "student_insight": "true",
        "assignment_data": latest_assignment,
        "assignment_name": assignment_name
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers)

        # Check the response
        if response.status_code == 200:
            print("Success:", response.json())
        else:
            print("Error:", response.status_code, response.text)

    except Exception as e:
        print("An error occurred:", str(e))

#update_insights("WAknrVCGE0enHYw7LDXGTIMatqo2")
