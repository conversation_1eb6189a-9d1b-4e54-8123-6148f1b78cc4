import numpy as np
import cv2
import base64

def crop_image_right_cv2(image, save_path=None, crop_width=110):
    """
    Crops the input image by removing a specified width from the right side using OpenCV.

    Parameters:
        image_path (str): Path to the input image.
        save_path (str, optional): Path to save the cropped image. If None, the image will not be saved.
        crop_width (int): Width to crop from the right side. Defaults to 30 pixels.

    Returns:
        numpy.ndarray: Cropped image array.
    """
    #image = cv2.imread(image_path)
    if image is None:
        raise ValueError("Could not read the image. Check the file path.")
    height, width= image.shape
    if crop_width >= width:
        raise ValueError("Crop width exceeds or matches image width.")
    cropped_image = image[:, :width - crop_width]
    if save_path:
        cv2.imwrite(save_path, cropped_image)
    return cropped_image


def identify_black_pixels(image, threshold=150) -> np.ndarray:
    """
    Identify all dark pixels in the image instead of just pure black ones.
    This avoids NumPy optimization issues and works regardless of cropping.
    """
    if len(image.shape) == 3:  # If the image has an alpha channel or is RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)  # Convert to grayscale
    
    black_pixels = np.argwhere(image < threshold)  # Detect all dark pixels
    print(f"[INFO] Number of black pixels found: {len(black_pixels)}")
    return black_pixels


def find_bounding_box(image,black_pixels: np.ndarray) -> tuple:
    """
    Given an array of black pixel coordinates, find the bounding box.
    black_pixels is assumed to be of shape (N, 2) with [row, col].
    Returns (x_min, x_max, y_min, y_max).
    """
    y_coords = black_pixels[:, 0]
    x_coords = black_pixels[:, 1]

    x_min = x_coords.min()
    x_max = x_coords.max()
    y_min = y_coords.min()
    y_max = y_coords.max()

    print(f"[INFO] Bounding box -> x_min: {x_min}, x_max: {x_max}, "
          f"y_min: {y_min}, y_max: {y_max}")
    cropped_image = image[y_min:y_max+1, x_min:x_max+1]
    #display(cropped_image)
    return x_min, x_max, y_min, y_max

def compute_padded_square_dimension(x_min: int, x_max: int, y_min: int, y_max: int) -> int:
    """
    Compute the dimension of the square such that it contains
    all black pixels with 5% extra padding on each side.
    """
    width = x_max - x_min + 1
    height = y_max - y_min + 1
    max_dim = max(width, height)

    padding = int(round(max_dim * 0.05))
    final_dim = max_dim + 2 * padding

    print(f"[INFO] Width: {width}, Height: {height}, Max dimension: {max_dim}, "
          f"Padding: {padding}, Final dimension: {final_dim}")
    return final_dim

def crop_centered_square(
    image: np.ndarray,
    x_min: int, x_max: int,
    y_min: int, y_max: int,
    final_dim: int
) -> np.ndarray:
    """
    1. Crop the image around the bounding box (x_min, x_max, y_min, y_max)
       plus 0.5% padding on all sides.
    2. Create a white final_dim x final_dim image.
    3. Center the cropped region onto that white square.
    """

    box_width = x_max - x_min + 1
    box_height = y_max - y_min + 1

    padding_w = int(0.005 * box_width)
    padding_h = int(0.005 * box_height)

    x_min_padded = x_min - padding_w
    x_max_padded = x_max + padding_w
    y_min_padded = y_min - padding_h
    y_max_padded = y_max + padding_h

    height, width = image.shape[:2]
    x_min_padded = max(0, x_min_padded)
    x_max_padded = min(width - 1, x_max_padded)
    y_min_padded = max(0, y_min_padded)
    y_max_padded = min(height - 1, y_max_padded)

    cropped_image = image[y_min_padded : y_max_padded + 1,
                          x_min_padded : x_max_padded + 1]

    if len(image.shape) == 3 and image.shape[2] == 3:
        square_image = np.full((final_dim, final_dim, 3), 255, dtype=image.dtype)
    else:

        square_image = np.full((final_dim, final_dim), 255, dtype=image.dtype)
    cropped_h, cropped_w = cropped_image.shape[:2]
    if cropped_w > final_dim or cropped_h > final_dim:

        center_x = cropped_w // 2
        center_y = cropped_h // 2
        half_final = final_dim // 2

        left = max(0, center_x - half_final)
        top  = max(0, center_y - half_final)


        if left + final_dim > cropped_w:
            left = cropped_w - final_dim
        if top + final_dim > cropped_h:
            top = cropped_h - final_dim


        cropped_image = cropped_image[top : top + final_dim,
                                      left : left + final_dim]

        cropped_h, cropped_w = cropped_image.shape[:2]

    x_offset = (final_dim - cropped_w) // 2
    y_offset = (final_dim - cropped_h) // 2
    square_image[y_offset : y_offset + cropped_h,
                 x_offset : x_offset + cropped_w] = cropped_image

    return square_image


def convert_image_to_base64(image: np.ndarray) -> str:
    """
    Converts a NumPy array image to a Base64-encoded string using OpenCV.
    """
    # Encode the image to PNG format (or any format OpenCV supports)
    success, encoded_image = cv2.imencode('.png', image)
    if not success:
        raise ValueError("[ERROR] Failed to encode image.")

    # Convert the encoded image to a Base64 string
    base64_image = base64.b64encode(encoded_image).decode('utf-8')

    return base64_image


def decode_base64_to_grayscale_image(base64_str):
    # Decode the base64 string directly to binary data
    binary_data = base64.b64decode(base64_str)
    # Convert binary data directly to an OpenCV image in grayscale mode
    image = cv2.imdecode(np.frombuffer(binary_data, np.uint8), cv2.IMREAD_GRAYSCALE)
    return image 

 

def crop_binary_image_to_square(image: np.ndarray) -> np.ndarray:
    """
    Main function:
    1. Identify black pixels in the image.
    2. Compute bounding box (x_min, x_max, y_min, y_max).
    3. Calculate the final square dimension with 5% padding.
    4. Crop and center the image to that square.
    Returns the resulting square image (still binary).
    """
    print(f"[INFO] Original image shape: {image.shape}")

    # 1. Identify black pixels
    image = crop_image_right_cv2(image)
    black_pixels = identify_black_pixels(image)

    # Edge case: if there are no black pixels, just return original
    if black_pixels.size == 0:
        print("[WARNING] No black pixels found; returning the original image.")
        image=convert_image_to_base64(image)
        return image

    # 2. Find bounding box
    x_min, x_max, y_min, y_max = find_bounding_box(image,black_pixels)

    # 3. Compute final square dimension with 5% padding
    final_dim = compute_padded_square_dimension(x_min, x_max, y_min, y_max)

    # 4. Crop and center the image
    cropped_square = crop_centered_square(image, x_min, x_max, y_min, y_max, final_dim)
    print("[INFO] Final cropped square shape:", cropped_square.shape)
    binary_cropped=convert_image_to_base64(cropped_square)
    return binary_cropped
