from service.grade_data_models import GradePipelineContext
from typing import Dict, Any
from service.grading.common_agents.grading_via_rubrics import grade_via_rubrics
from service.context_pipeline import logger

def grade_via_rubrics_node(state: GradePipelineContext) -> Dict[str, Any]:
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting grade_via_rubrics_node")

    assignment = state.assignment
    student_profile = state.student
    context = state.assignment.passage if state.assignment.passage else ""
    assignment_type = state.request.assignment_type
    dyslexia = state.student.is_dyslexic
    previous_assignment = state.previous_response


    try:
        updated_assignment,overall_grade = grade_via_rubrics(
            assignment=assignment,
            student_profile=student_profile,
            context=context,
            assignment_type=assignment_type,
            dyslexia=dyslexia,
            old_assignment=previous_assignment
        )
        
        return {"assignment": updated_assignment,"overall_grade":overall_grade}

    except Exception as e:
        logger.error(f"Error in assess_strengths_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}