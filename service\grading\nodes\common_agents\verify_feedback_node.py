from service.grade_data_models import GradePipelineContext
from service.grading.common_agents.safeguard import verify_feedback
from service.context_pipeline import logger

def verify_feedback_node(state: GradePipelineContext):
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting verify_feedback_node")

    assignment = state.assignment
    assignment_type = state.request.assignment_type

    try:
        updated_assignment = verify_feedback(
                assignment, assignment_type
            )
        return {"assignment": updated_assignment}

    except Exception as e:
        logger.error(f"Error in verify_feedback_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}