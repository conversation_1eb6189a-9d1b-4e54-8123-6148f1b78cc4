import openai
import json
from service.utils.validate_response import validate_output_structure
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception
from timeout_decorator import timeout, TimeoutError

def is_not_timeout(exception):
    """
    Checks if an exception is NOT a timeout error.
    
    This is used in the retry mechanism to determine if retries should occur 
    when an exception other than TimeoutError is encountered.

    Parameters:
    - exception (Exception): The exception to check.

    Returns:
    - bool: True if the exception is not a TimeoutError, False otherwise.
    """
    return not isinstance(exception, TimeoutError)

# @retry(stop=stop_after_attempt(5), wait=wait_fixed(2), retry=retry_if_exception(is_not_timeout))
# @timeout(30)  # Uncomment if timeout enforcement is needed
# @retry(stop=stop_after_attempt(5), wait=wait_fixed(2))  # Uncomment for retry mechanism
def make_openai_vision_request(message, tools, temperature, images, description=[]):
    """
    Sends a request to OpenAI's gpt-4.1-nano model with vision capabilities, 
    incorporating images and optional descriptions.

    Parameters:
    - message (list): The message history in OpenAI chat format.
    - tools (list): The list of tools (functions) to be used in the request.
    - temperature (float): The temperature value controlling response randomness.
    - images (list): A list of base64-encoded image strings to be processed.
    - description (list, optional): A list of descriptions corresponding to images. Defaults to an empty list.

    Returns:
    - dict: The structured response from OpenAI's API after processing.
    
    Raises:
    - Exception: If there is an issue with the OpenAI API call.
    """

    print("USING VISION")

    # Constructing the request payload for OpenAI Vision
    vision_request = {
        "role": "user",
        "content": [],
    }

    # Attaching images and optional descriptions
    for index, image in enumerate(images):
        base64_image = image
        content_entry = []

        # Include description if provided
        if description and index < len(description):
            content_entry.append({
                "type": "text",
                "text": f"Here is the description of the image-{index} containing the student's answer: {description[index]}"
            })

        # Add image data
        content_entry.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{base64_image}",
                "detail": "high"
            }
        })

        # Append content entries to the vision request
        vision_request["content"].extend(content_entry)

    # Append the vision request to the main message list
    message.append(vision_request)
    #print("Message used:", message)

    try:
        # Sending request to OpenAI gpt-4.1-nano with vision capabilities
        response = openai.chat.completions.create(
            model="gpt-4.1-nano",
            temperature=temperature,
            messages=message,
            tools=tools,
            tool_choice="required"
        )
    except Exception as e:
        print("ERROR IN OPENAI VISION REQUEST:", e)
        raise e

    # Extracting the response data
    data = response.choices[0].message.tool_calls[0].function.arguments
    print("GPT RESPONSE:", data)

    # Handling cases where the response structure includes 'tool_uses'
    if 'tool_uses' in data:
        data = data['tool_uses'][0]['parameters']

    print("Response from GPT:", data)

    # Parsing the JSON response
    data = json.loads(data)

    # Validating the response against expected tool structure
    verify_output = validate_output_structure(data, tools)

    print(type(data))
    return data
