def agent(assignment_type, use_vision,dyslexia=False):
    list_of_questions = [
    # A. Checks for 2-Credit Criteria
    "Does the student include at least one valid inference or claim drawn directly from the text(s)?",
    "Does the student’s response go beyond simple summary to provide genuine analysis of the text(s)?",
    "Does the student incorporate relevant facts, definitions, or concrete details from the text(s) to support their points?",
    "Has the student provided enough textual evidence or information to fully address all requirements of the prompt?",
    "Does the student consistently write in complete sentences with errors that do not interfere with overall readability?",
    
    # B. Checks for 1-Credit Criteria
    "Does the response include deeper analysis, rather than relying mostly on retelling or listing events/details from the text(s)?",
    "Even if the response is largely summary-based, does the student still include relevant facts, definitions, or concrete details from the text(s)?",
    "Does the response use complete sentences and a fully developed paragraph format (rather than incomplete sentences or bullet points)?",
    
    # C. Checks for 0-Credit Criteria
    "Does the response address the prompt’s requirements accurately, without being wholly off-topic or incorrect?",
    "Is the response provided in intelligible English (i.e., it is neither non-English nor indecipherable)?",
    "Did the student provide a response (i.e., it is not left blank)?",
    
    # D. Additional Scoring Notes
    "If the prompt requires referencing two texts, does the student reference both? (If 'No,' the score cannot exceed 1.)"
]


    return list_of_questions
