from service.context_pipeline import logger,aisdk_object
from service.grade_data_models import GradeRequest,GradePipelineContext
from aisdk.data_models.people import Student
import random

def pre_process_grade_assignment(data,default_ctx,request_id):
    """Pre-process grade assignment"""
    try:
        request_body=GradeRequest(**data)
        logger.info(f"Request body validated: {request_body}")
        #default_ctx.request=request_body
    except Exception as e:
        logger.error("Error retrieving student profile: %s", e)
    # if 'interests' in data:
    #     if isinstance(data['interests'], list):
    #         data['interest'] = random.choice(data['interests'])
    #     else:
    #         data['interest'] = data['interests']
    try:
        last_attempt=aisdk_object.firestore.get_latest_attempts_data(data['student_id'],data['assignment_type'],data['assignment_id'])
    except:
        last_attempt=None
        logger.info("No previous attempt found")

    default_ctx_dictionary = default_ctx.dict()
    default_ctx_dictionary['request_id']=request_id
    default_ctx_dictionary['student'] = Student(**data)
    default_ctx_dictionary['request']=request_body
    default_ctx_dictionary['previous_response']=last_attempt
    challenges = default_ctx_dictionary["assignment"]["challenges"]
    answers=data['answers']
    idx_to_challenge = {str(ch["index"]): ch for ch in challenges}

    for ans in answers:
        qnum = str(ans.get("question_number"))
        if qnum not in idx_to_challenge:     
            continue
        challenge = idx_to_challenge[qnum]
        for key, value in ans.items():
            if key != "question_number":
                challenge[key] = value
        logger.info(f"Challenge updated with answer: {challenge}")

    
    try:
        ptx=GradePipelineContext(**default_ctx_dictionary)
        logger.info(f"GradePipelineContext created: {ptx}")
    except Exception as e:
        logger.error(f"Error creating GradePipelineContext: {e}")
    return ptx
