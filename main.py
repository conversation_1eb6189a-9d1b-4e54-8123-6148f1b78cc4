from flask import make_response, jsonify
from service.utils.insights import update_insights
from service.context_pipeline import aisdk_object,logger
from service.pre_process_grading import pre_process_grade_assignment
import json
import ast
from service.grade_flow_manager import route_assignment

@aisdk_object.auth_decorator
def grade_assignment(request):
    """Main function to grade assignments"""
    request_id = request.headers.get("X-Cloud-Trace-Context", "No request ID found")
    global logger
    logger.info(f"request_id: {request_id}")
    form_data = dict(request.form)
    try:
        if request.method == 'OPTIONS':
            headers = {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                'Access-Control-Max-Age': '3600'
            }
            return ('', 204, headers)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        error_message = {
            "error": str(e),
            "message": "An error occurred while grading the assignment",
            "request_id": request_id
        }
        return (error_message, 500, {})

    try:
        headers = {'Access-Control-Allow-Origin': '*'}
        logger.info(f"Request form data:{form_data}")
        data={
            "student_id": form_data.get('student_id'),
            "teacher_id": form_data.get('teacher_id'),
            "class_id": form_data.get('class_id'),
            "assignment_id": form_data.get('assignment_id'),
            "session_id": form_data.get('session_id'),
            "assignment_type": form_data.get('assignment_type'),
        }
        try: 
            if isinstance(form_data.get('answers'), str):
                logger.info(f"Answer:{request.form.get('answers')}")
                form_data['answers']=json.loads(form_data.get('answers'))
            else:
                form_data['answers']=form_data.get('answers')
        except:
            logger.info("json loads failed")
            try:
                logger.info("ast literal eval used")
                form_data['answers']=ast.literal_eval(form_data.get('answers'))
            except:
                logger.info("ast literal eval failed")
        default_ctx=aisdk_object.set_pipeline_context(data)
        logger=aisdk_object.get_logging_handler()
        langfuse_handler=aisdk_object.get_langfuse_handler(request_id)
        ctx = pre_process_grade_assignment(form_data,default_ctx,request_id)
        #logger.info(f"Context after pre-processing: {ctx}")
        response=route_assignment(ctx,langfuse_handler)
        logger.info(f"graded assignment:{response}")

    #     if isinstance(data['assignment'], list):
    #         for entry in data['assignment']:
    #             entry['proof_of_work'] = True
    #             entry['reasoning'] = ""
    #     else:
    #         data['assignment']['proof_of_work'] = True
    #         data['assignment']['reasoning'] = ''

    #     # Import appropriate grading flow
    #     if 'math' in data['assignment_type']:
    #         from service.grading.graphs.math.math_flow import flow
    #     elif 'history' in data['assignment_type']:
    #         from service.grading.graphs.history.history_flow import flow
    #     else:
    #         from service.grading.graphs.ela.ela_flow import flow

    #     # Process assignment
    #     if data['use_vision']:
    #         globals.assignment_length = len(data['assignment']) if isinstance(data['assignment'], list) else 1
    #         final_results = flow(
    #             data['assignment'],
    #             student_profile,
    #             data['assignment_type'],
    #             data['student_id'],
    #             data['teacher_id'],
    #             data['session_id'],
    #             data['context'],
    #             data['use_vision'],
    #             data['question_number'],
    #             data['files_base64'],
    #             language=data['language'],
    #             dyslexia=data['dyslexic'],
    #             old_assignment=data['old_assignment']
    #         )
    #     else:
    #         final_results = flow(
    #             data['assignment'],
    #             student_profile,
    #             data['assignment_type'],
    #             data['student_id'],
    #             data['teacher_id'],
    #             data['session_id'],
    #             data['context'],
    #             language=data['language'],
    #             dyslexia=data['dyslexic'],
    #             old_assignment=data['old_assignment']
    #         )

    #     # Add request ID to results
    #     final_results['request_id'] = data['request_id']

    #     # Update indexes if needed
    #     if 'index_data' in data and 'essay' not in data['assignment_type']:
    #         for result in final_results['challenges']:
    #             if result['index'] in data['index_data']:
    #                 result['index'] = data['index_data'][result['index']]

    #     # Log results and update insights
    #     update_insights(
    #         data['class_id'],
    #         data['student_id'],
    #         data['assignment_name'],
    #         final_results
    #     )

    #     # # Upload to storage and send QA request (if not staging test)
    #     # if data['session_id'] != 'staging-test':
    #     #     upload_to_storage(
    #     #         data['session_id'],
    #     #         inputs,
    #     #         final_results,
    #     #         data['files_base64'],
    #     #         data['files_names'],
    #     #         data['use_vision'],
    #     #         data['name'],
    #     #         data['class_id'],
    #     #         data['student_id'],
    #     #         data['request_id']
    #     #     )
        
    #     # send_qa_request(inputs, final_results)

    #     return add_cors_headers(jsonify(final_results))

    # except InputValidationError as e:
    #     logger.error(f"Input validation error: {str(e)}")
    #     error_message = {"error": str(e), "request_id": data.get('request_id', 'unknown')}
    #     return make_response((jsonify(error_message), 400))
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        error_message = {
            "error": str(e),
            "message": "An error occurred while grading the assignment",
            "request_id": data.get('request_id', 'unknown')
        }
        return make_response((jsonify(error_message), 500))


