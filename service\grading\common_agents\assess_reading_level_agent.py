import textstat
import re

def kincaid_grade_level_test_english(text):
    """
    Calculates the Flesch-Kincaid grade level for an English text.
    
    Purpose:
    - This function uses the Flesch-Kincaid grade level formula to assess the readability 
      of an English text and estimate the required grade level for understanding.

    Parameters:
    - text (str): The input text for which to calculate the reading level.
    
    Returns:
    float: The Flesch-Kincaid grade level for the given text.
    """
    return textstat.flesch_kincaid_grade(text)

def kincaid_grade_level_test_spanish(text):
    """
    Calculates the reading level for a Spanish text using the Fernández-Huerta formula.
    
    Purpose:
    - This function uses the Fernández-Huerta formula to calculate the reading level 
      of a Spanish text, which is suitable for assessing text complexity in Spanish.

    Parameters:
    - text (str): The input text for which to calculate the reading level.
    
    Returns:
    float: The Fernández-Huerta reading level for the given Spanish text.
    """
    return textstat.fernandez_huerta(text)
    # Optionally use other Spanish reading level formulas:
    # return textstat.szigriszt_pazos(text)
    # return textstat.gutierrez_polini(text)
    # return textstat.craw<PERSON>(text)

def extract_and_convert_grade(grade_str):
    """
    Extracts a numeric grade level from a string and adjusts it if necessary.
    
    Purpose:
    - This function extracts a numeric grade level from a string input, adjusts it if necessary
      (for example, changing '4' to '5'), and returns the grade level as an integer.

    Parameters:
    - grade_str (str or int): The string containing the grade level or the integer itself.
    
    Returns:
    int: The extracted numeric grade level, or None if not found.
    """
    if isinstance(grade_str, int):
        return grade_str
    numbers = re.findall(r'\d+', grade_str)
    if numbers:
        # Adjust grade level if necessary
        if numbers[0] == '4':
            numbers[0] = '5'
        return int(numbers[0])
    else:
        return None
