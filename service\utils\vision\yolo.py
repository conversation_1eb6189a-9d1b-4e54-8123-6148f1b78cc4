import requests
import base64
import cv2
import numpy as np
from PIL import Image
from io import BytesIO
import time
from google.cloud import storage


def get_bounding_boxes(image):
    """
    Sends a request to a YOLO-based object detection API to retrieve bounding boxes for detected objects.

    Parameters:
    - image (str): Base64-encoded image string.

    Returns:
    - list: A list of bounding boxes detected in the image. If an error occurs, an empty list is returned.
    """
    url = "https://yolo-hbm5fbifva-uc.a.run.app/detect_64/"
    payload = {'b64_image': image}
    response = requests.post(url, json=payload)

    print('Getting bounding boxes', response.json())

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        return []


def crop_image(image_binary, bbox, session_id, bbox_format='xywh'):
    """
    Crops an image based on a bounding box and uploads the cropped image to Google Cloud Storage.

    Parameters:
    - image_binary (str): Base64-encoded image string.
    - bbox (tuple): Bounding box coordinates.
    - session_id (str): Unique identifier for the session, used for naming the uploaded image.
    - bbox_format (str, optional): Format of the bounding box ('xywh' for x, y, width, height or 'xyxy' for top-left and bottom-right). Defaults to 'xywh'.

    Returns:
    - str: Base64-encoded cropped image if successful, otherwise returns the original image.
    """
    # Decode base64 image
    decoded_data = base64.b64decode(image_binary)
    np_data = np.frombuffer(decoded_data, np.uint8)  # Updated deprecated np.fromstring to np.frombuffer
    image = cv2.imdecode(np_data, cv2.IMREAD_UNCHANGED)

    # Determine bounding box coordinates
    if bbox_format == 'xyxy':
        x1, y1, x2, y2 = bbox
    elif bbox_format == 'xywh':
        x1, y1, w, h = bbox
        x2 = x1 + w
        y2 = y1 + h
    else:
        raise ValueError("Invalid bounding box format. Use 'xyxy' or 'xywh'.")

    print("Before cropping:", x1, y1, x2, y2)

    height, width = image.shape[:2]

    # Adjust the bounding box to ensure better visibility
    y1 = max(0, y1 - 400)
    y2 = min(height, y2 + 200)
    x1 = max(0, x1 - 30)
    x2 = min(width, x2 + 30)

    print("After cropping:", x1, y1, x2, y2)
    print(f"Cropping image with coordinates: ({x1}, {y1}, {x2}, {y2})")

    try:
        # Ensure coordinates remain within valid image boundaries
        y1_crop = max(0, int(y1))
        y2_crop = min(int(y2), height)
        x1_crop = max(0, int(x1))
        x2_crop = min(int(x2), width)

        # Crop the image
        cropped_image = image[y1_crop:y2_crop, x1_crop:x2_crop, :]

        # Convert cropped image to PIL format
        cropped_image = Image.fromarray(cropped_image)
        output = BytesIO()
        cropped_image.save(output, format='JPEG')
        cropped_image_binary = output.getvalue()

        try:
            # Upload cropped image to Google Cloud Storage
            blob_url = upload_image_to_bucket(cropped_image_binary, session_id)
            print(f"Image uploaded to {blob_url}")
        except Exception as e:
            print(f"An error occurred while uploading image to bucket: {e}")
            blob_url = None  # Set blob_url to None in case of failure

        # Encode cropped image in base64
        cropped_image_base64 = base64.b64encode(cropped_image_binary).decode('utf-8')
        return cropped_image_base64
    except Exception as e:
        print(f"Error cropping image: {e}. Returning original image.")
        return image_binary


def upload_image_to_bucket(image_data, session_id):
    """
    Uploads an image to Google Cloud Storage.

    Parameters:
    - image_data (bytes): Binary image data to upload.
    - session_id (str): Unique session identifier used for naming the uploaded image.

    Returns:
    - str: Public URL of the uploaded image.
    """
    # Initialize Google Cloud Storage client
    client = storage.Client()
    bucket = client.bucket('yolov8-finetuning')

    # Define unique filename for the uploaded image
    blob = bucket.blob(f'labeled_images/labeled_{session_id}_{int(time.time())}.jpeg')

    # Upload image to Google Cloud Storage
    blob.upload_from_string(image_data, content_type='image/jpeg')

    # Return the public URL of the uploaded image
    return blob.public_url
