def validate_output_structure(output, tools, num_questions=None):
    """
    This function validates the structure of an assignment response based on the tools used. 
    It ensures that the response generated from openai matches the structure used in the tools.
 
    Parameters:
    - tools (list): The list of JSON response used as tools for openai.
    - ouptut (list): The list of JSON response generated by openai. Make sure that the response is not in string format.
    - num_questions (int): The expected number of challenges or questions in the assignment.
 
    Raises:
    - AssertionError: If the response structure does not meet the required format or if the number of challenges does not match the expected count.
    """
    print("Validating output structure")
    # print(f"Validating if 'tools' is a list: {type(tools)}")
    assert isinstance(tools, list), "Input 'tools' should be a list"
    # print(f"Validating if 'tools' contains exactly one item: {len(tools)}")
    assert len(tools) == 1, "Input 'tools' should have exactly one item"
    # print(f"Validating if the first item in 'tools' is a dictionary: {type(tools[0])}")
    assert isinstance(tools[0], dict), "The first item in 'tools' should be a dictionary"
    tool = tools[0]
    # print(f"Validating if 'type' key exists in tool: {tool.keys()}")
    assert "type" in tool, "'type' key is missing in the tool"
    # print(f"Validating if tool's type is 'function': {tool['type']}")
    assert tool["type"] == "function", "The tool's type should be 'function'"
    # print(f"Validating if 'function' key exists in tool: {tool.keys()}")
    assert "function" in tool, "'function' key is missing in the tool"
    # print(f"Validating if 'function' is a dictionary: {type(tool['function'])}")
    assert isinstance(tool["function"], dict), "'function' should be a dictionary"
    # print(f"Validating if 'parameters' key exists in function: {tool['function'].keys()}")
    assert "parameters" in tool["function"], "'parameters' key is missing in the function"
    # print(f"Validating if 'parameters' is a dictionary: {type(tool['function']['parameters'])}")
    assert isinstance(tool["function"]["parameters"], dict), "'parameters' should be a dictionary"
    parameters = tool["function"]["parameters"]
    validate_parameters(parameters, output, num_questions)
    return True
 
 
def validate_parameters(parameters, output, num_questions=None):
    param_type = parameters.get("type")
    # print(f"Validating parameters type: {param_type}")
    if param_type == "object":
        # print("Validating 'properties' key for object type")
        assert "properties" in parameters, "'properties' key is missing for object type"
        properties = parameters["properties"]
        # print(f"Validating if 'properties' is a dictionary: {type(properties)}")
        assert isinstance(properties, dict), "'properties' should be a dictionary"
        # print(f"Validating if output is a dictionary: {type(output)}")
        assert isinstance(output, dict), "'object should be dictionary'"
        for key, prop in properties.items():
            # print(f"Validating if key '{key}' exists in output: {output.keys()}")
            assert key in output, f"Key '{key}' is missing in the output"
            validate_parameters(prop, output.get(key), num_questions)
 
    elif param_type == "array":
        # print(f"Validating if output is a list: {type(output)}")
        assert isinstance(output, list), f"Expected list for array type but got {type(output)}"
        # print(f"Validating if 'items' key exists in parameters: {parameters.keys()}")
        assert "items" in parameters, "'items' key is missing for array type"
        # print(f"Validating the number of generated questions: {len(output)} (expected: {num_questions})")
        # print(output)
        if 'index' in output[0] and num_questions is not None:
            assert len(output) == int(num_questions), "Incorrect number of questions generated"
        for item in output:
            validate_parameters(parameters["items"], item, num_questions)
 
    elif param_type == "boolean":
        # print(f"Validating if output is a boolean: {type(output)}")
        assert isinstance(output, bool), f"Expected boolean but got {type(output)}"
    elif param_type == "string":
        # print(f"Validating if output is a string: {type(output)}")
        assert isinstance(output, str), f"Expected string but got {type(output)}"
    elif param_type == "integer":
        # print(f"Validating if output is an integer: {type(output)}")
        assert isinstance(output, int), f"Expected integer but got {type(output)}"
 
    else:
        raise AssertionError(f"Unsupported type '{param_type}' encountered in parameters")