
from service.utils.supporting_functions import reading_level_example
from service.logging import log_function_io
from service.utils.gpt_call import make_openai_request_with_tools
import globals
from service.common_agents.feedback_questions_agent import get_feedback_by_grade

import os

from service.langfuse_client import langfuse

load_dotenv()

langfuse = Langfuse(
    secret_key= os.getenv("LANGFUSE_SECRET_KEY"),
    public_key= os.getenv("LANGFUSE_PUBLIC_KEY"),
    host="https://cloud.langfuse.com"
)

@log_function_io
def fix_feedback_language(feedback_list, reading_level):
    example_text = reading_level_example(reading_level)
    nb_of_questions = globals.assignment_length

    messages = langfuse.get_prompt("fix_feedback_language", label="latest")
    complete_chat_prompt = messages.compile(
        feedback_list=feedback_list,
        reading_level=reading_level,
        nb_of_questions=nb_of_questions,
        example_text=example_text
    )

    tools=[
            {
            "type": "function",
            "function": {
                "name": "diversify_feedback",
                "description": "Diversify the language of the feedback used.",
                "parameters": {
                    "type": "object",
                    'properties': {
                    "new_feedback": {
                            "type": "array",
                            "items": {
                            "type": "object",
                            "properties": {
                                "index": {
                                    "type": "string",
                                    "description": "The question number"
                                },
                                "personalized_feedback": {
                                    "type": "string",
                                    "description": "personalized_feedback after diversifying the language used."
                                }
                            },
                            "required":["index","personalized_feedback"],
                            },
                        },
                    },
                    "required":["new_feedback"]    
                    }
                }
            }
        ]

    new_feedback_list=make_openai_request_with_tools(complete_chat_prompt,tools,0.7,response_length=len(feedback_list))
    if len(feedback_list)!=len(new_feedback_list['new_feedback']):
        print("The number of feedbacks mismatch!")
        missing_feedbacks = []
        for feedback in feedback_list:
            if not any(new_feedback['index'] == feedback['index'] for new_feedback in new_feedback_list['new_feedback']):
                missing_feedbacks.append(feedback)
        new_feedback_list['new_feedback'].extend(missing_feedbacks)

        # fix_feedback_language(feedback_list, reading_level)
    return (new_feedback_list['new_feedback'])

