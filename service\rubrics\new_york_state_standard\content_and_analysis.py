def agent(assignment_type, use_vision,dyslexia=False):
    list_of_questions = [
    # A. Introduction of Topic
    "Does the student introduce a clearly defined topic that directly aligns with the task and purpose?",
    "Does the introduction include a compelling hook or an insightful opening that captures the reader’s interest?",
    "Does the student provide enough relevant background or context to help the reader understand the topic and its importance?",
    "Is the introduction consistently focused on the main idea, without straying from the stated topic or purpose?",
    "Does the introduction remain relevant and on-task throughout, effectively setting the stage for the analysis?",
    
    # B. Comprehension and Critical Analysis
    "Does the student demonstrate a nuanced comprehension of the text(s), going beyond surface-level facts?",
    "Does the student offer original or thought-provoking interpretations that suggest critical thinking?",
    "Does the response draw connections between the text(s) and broader real-world issues, themes, or implications?",
    "Does the student include sufficient and appropriate textual details to support their analysis?",
    "Does the analysis go beyond merely summarizing the text(s), providing deeper interpretation or critical insight?",
    
    # C. Integration and Elaboration of Ideas
    "Does the student smoothly integrate evidence (quotations, references, data) that reinforce the main argument?",
    "Does the response use precise details, examples, and explanations that strengthen the analysis?",
    "Does the student synthesize information from multiple parts of the text(s) or different sources to build a cohesive argument?",
    "Are the connections between ideas or pieces of evidence logical and clearly explained?",
    "Is the writing well-organized, with clear links between evidence and the student’s main points?"
]



    return list_of_questions
