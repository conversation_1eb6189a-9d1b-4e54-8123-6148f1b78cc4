# service/graphs/ela/aipassage_langgraph.py
from langgraph.graph import StateGraph
from service.grade_data_models import GradePipelineContext
from service.context_pipeline import logger
from service.grading.nodes.common_agents.grading_via_rubrics_node import grade_via_rubrics_node

def general_ela_graph(ptx:GradePipelineContext,langfuse_handler):
    logger.info("Creating Passage Based Assignment Graph (Example Pattern Version)")
    try:
        workflow = StateGraph(GradePipelineContext)
        workflow.add_node('grade_via_rubrics', grade_via_rubrics_node)
        # workflow.add_node('summarize_feedback', summarize_feedback)
        # workflow.add_node('verify_feedback', verify_feedback)
        # workflow.add_node('answer_feedback_questions', translate_response)
        workflow.set_entry_point('grade_via_rubrics')
        chain = workflow.compile()
    except Exception as e:
        logger.error(f"Error creating graph: {e}", exc_info=True)
        raise

    try:
        logger.info("Invoking graph...")
        result = chain.invoke(ptx)
        logger.info(f"Graph invoked successfully: {result}")
        return result

    except Exception as e:
        logger.error(f"Error invoking graph: {e}", exc_info=True) 
