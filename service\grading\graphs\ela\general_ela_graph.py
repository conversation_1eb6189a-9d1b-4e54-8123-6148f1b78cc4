# service/graphs/ela/aipassage_langgraph.py
from langgraph.graph import StateGraph,END
from service.grade_data_models import GradePipelineContext
from service.context_pipeline import logger
from service.grading.nodes.common_agents.grading_via_rubrics_node import grade_via_rubrics_node
from service.grading.nodes.common_agents.summarize_personalized_feedback_node import summarize_personalized_feedback_node
from service.grading.nodes.common_agents.answer_feedback_questions_node import answer_feedback_questions_node
from service.grading.nodes.common_agents.verify_feedback_node import verify_feedback_node

def general_ela_graph(ptx:GradePipelineContext,langfuse_handler):
    logger.info("Creating Passage Based Assignment Graph (Example Pattern Version)")
    try:
        workflow = StateGraph(GradePipelineContext)
        workflow.add_node('grade_via_rubrics', grade_via_rubrics_node)
        workflow.add_node('summarize_feedback', summarize_personalized_feedback_node)
        workflow.add_node('answer_feedback_questions', answer_feedback_questions_node)
        workflow.add_node('verify_feedback', verify_feedback_node)
        # workflow.add_node('answer_feedback_questions', translate_response)
        workflow.set_entry_point('grade_via_rubrics')
        workflow.add_edge('grade_via_rubrics', 'summarize_feedback')
        workflow.add_edge('summarize_feedback', 'answer_feedback_questions')
        workflow.add_edge('answer_feedback_questions', 'verify_feedback')
        workflow.add_edge('verify_feedback', END)
        chain = workflow.compile()
    except Exception as e:
        logger.error(f"Error creating graph: {e}", exc_info=True)
        raise

    try:
        logger.info("Invoking graph...")
        result = chain.invoke(ptx)
        logger.info(f"Graph invoked successfully: {result}")
        return result

    except Exception as e:
        logger.error(f"Error invoking graph: {e}", exc_info=True) 
