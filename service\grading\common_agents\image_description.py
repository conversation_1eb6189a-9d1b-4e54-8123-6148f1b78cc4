from service.vision.vision import make_openai_vision_request
from service.gemini.gemini_api import make_gemini_vision_request
import os

from service.langfuse_client import langfuse

load_dotenv()

langfuse = Langfuse(
    secret_key= os.getenv("LANGFUSE_SECRET_KEY"),
    public_key= os.getenv("LANGFUSE_PUBLIC_KEY"),
    host="https://cloud.langfuse.com"
)
def describe_image(image):
    print("IMAGE IN Describe image:",image)
    messages = langfuse.get_prompt("describe_image", label="latest")

    tools=[
            {
                "type": "function",
                "function": {
                    "name": "describe_image",
                    "description": "Describe the image provided in detail.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "image_description": {
                                    "type": "string",
                                    "description": "detailed description of the image"
                                    },
                            "information_accuracy": {
                                    "type": "string",
                                    "description": "information about the image based on its accuracy"
                                    },
                                    },
                                "required": ["image_description","information_accuracy"]
                    }
                }
            }
        ]
    try:
        if type(image) != list:
            try:
                description = make_openai_vision_request(messages, tools, 0.5, [image])
            except Exception as e_openai:
                print(f"OpenAI Vision request failed: {str(e_openai)}. Falling back to Gemini Vision.")
                try:
                    description = make_gemini_vision_request(messages, tools, 0.5, [image])
                except Exception as e_gemini:
                    print(f"Gemini Vision request failed: {str(e_gemini)}. No description generated.")
                    description = None  # Handle failure case appropriately
        else:
            try:
                description = make_openai_vision_request(messages, tools, 0.5, image)
            except Exception as e_openai:
                print(f"OpenAI Vision request failed: {str(e_openai)}. Falling back to Gemini Vision.")
                try:
                    description = make_gemini_vision_request(messages, tools, 0.5, image)
                except Exception as e_gemini:
                    print(f"Gemini Vision request failed: {str(e_gemini)}. No description generated.")
                    description = None  # Handle failure case appropriately
    except Exception as e:
        print(f"Unexpected error while generating description: {str(e)}")
        description = None  # Ensure variable is always set, even in failure cases
   
    print("IMAGE DESCRIPTION:",description)
    return description