def agent(assignment_type, use_vision=False,dyslexia=False):
    list_of_questions = [
    # A. Grammar and Syntax
    "Does the student consistently use correct subject-verb agreement, verb tenses, pronouns, and modifiers throughout the writing?",
    "Does the writing include a variety of sentence structures (simple, compound, complex) used effectively?",
    "Are any grammar errors minor and not distracting from the overall meaning?",
    "Is the text generally free of frequent or significant grammar errors that could hinder comprehension or readability?",
    "Does the text remain coherent throughout, avoiding major grammar or syntax errors (e.g., run-ons, fragments, misplaced modifiers)?",
    
    # B. Punctuation and Capitalization
    "Does the student use punctuation marks (commas, periods, semicolons, quotation marks, etc.) appropriately and consistently?",
    "Does the student correctly capitalize sentence beginnings, proper nouns, and titles throughout the writing?",
    "Are punctuation or capitalization mistakes minor and infrequent, without causing confusion?",
    "Is the writing generally free of punctuation and capitalization errors that would obscure meaning or disrupt flow (e.g., missing commas, random capitalization)?",
    "Are punctuation and capitalization applied correctly, ensuring the text remains easy to follow?",
    
    # D. Clarity and Readability
    "Is the writing clear, with minimal errors that do not impede understanding?",
    "Are any existing errors minor enough that they do not significantly distract from the text’s message?",
    "Is the writing sufficiently clear so that the reader does not need to re-read sections to grasp the meaning?",
    "Are errors sufficiently limited so that they do not impede comprehension of the text?",
    "Is the text free of severe grammatical failures or widespread errors, ensuring overall readability?"
]

    if dyslexia:
        add_questions = [    # C. Spelling
    "Does the student consistently spell grade-level words correctly, including challenging vocabulary?",
    "Are commonly confused words (e.g., their/there/they’re) used correctly?",
    "Are spelling errors rare and limited to more complex words, without confusing the reader?",
    "Does the student avoid noticeable spelling mistakes on common words, ensuring clarity?",
    "Does the student avoid numerous spelling errors that would make words unrecognizable or the text unreadable?",]
        list_of_questions.extend(add_questions)
    return list_of_questions
