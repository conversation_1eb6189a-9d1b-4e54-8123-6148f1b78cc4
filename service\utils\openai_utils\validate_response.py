import globals

def validate_output_structure(output,tools,response_length=None):
    """
    This function validates the structure of an assignment response based on the tools used. 
    It makes sure that the response generated from the openai matches the structure used in the tools.

    Parameters:
    - tools (list): The list of JSON response used as tools for openai.
    - ouptut (list): The list of JSON response generated by openai. make sure that the response is not string format.

    Raises:
    - AssertionError: If the response structure does not meet the required format or if the number of challenges does not match the expected count.
    """
    print("Asserting Output Structure")
    assert isinstance(tools, list), "Input 'tools' should be a list"
    assert len(tools) == 1, "Input 'tools' should have exactly one item"
    assert isinstance(tools[0], dict), "The first item in 'tools' should be a dictionary"
    
    tool = tools[0]
    assert "type" in tool, "'type' key is missing in the tool"
    assert tool["type"] == "function", "The tool's type should be 'function'"
    
    assert "function" in tool, "'function' key is missing in the tool"
    assert isinstance(tool["function"], dict), "'function' should be a dictionary"
    
    assert "parameters" in tool["function"], "'parameters' key is missing in the function"
    assert isinstance(tool["function"]["parameters"], dict), "'parameters' should be a dictionary"
    
    parameters = tool["function"]["parameters"]
    #print("BEFORE RESPONSE LENGHT:",response_length)
    validate_parameters(parameters, output,response_length)
    return True


def validate_parameters(parameters, output,response_length=None):
    print("Validating Parameters")
    param_type = parameters.get("type")
    
    if param_type == "object":
        assert "properties" in parameters, "'properties' key is missing for object type"
        properties = parameters["properties"]
        
        assert isinstance(properties, dict), "'properties' should be a dictionary"
        assert isinstance(output,dict), "'object should be dictionary'"
        for key, prop in properties.items():
            assert key in output, f"Key '{key}' is missing in the output"
            print(f"Validating {key}")
            validate_parameters(prop, output.get(key),response_length)

    elif param_type == "array":
        assert isinstance(output, list), f"Expected list for array type but got {type(output)}"
        assert "items" in parameters, "'items' key is missing for array type"
        if 'index' in output[0]:
            if response_length:
                pass
                # print("RESPONSE LENGTH:",len(output),response_length)
                # assert len(output) == response_length, "Incorrect number of questions generated"
            else:
                print("GLOBAl LENGTH:",len(output),int(globals.assignment_length))
                print("Response lgnth:",response_length)
                assert len(output) == int(globals.assignment_length), "Incorrect number of questions generated"
        for item in output:
            validate_parameters(parameters["items"], item,response_length)

    elif param_type == "boolean":
        assert isinstance(output, bool), f"Expected boolean but got {type(output)}"
    
    elif param_type == "string":
        assert isinstance(output, str), f"Expected string but got {type(output)}"
    
    elif param_type == "integer":
        assert isinstance(output, int), f"Expected integer but got {type(output)}"

    else:
        raise AssertionError(f"Unsupported type '{param_type}' encountered in parameters")