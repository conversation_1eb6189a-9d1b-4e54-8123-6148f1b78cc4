from service.utils.gpt_call import make_openai_request_with_tools
from service.vision.vision import make_openai_vision_request
from service.utils.supporting_functions import reading_level_example,split_rubric_responses,update_tools_structure,organize_feedback
from service.logging import log_function_io
from service.vision.simplify_challenges import simplify_challenges
import ast
import concurrent.futures
from service.common_agents.assess_reading_level_agent import extract_and_convert_grade,kincaid_grade_level_test_english
from service.common_agents.reading_level_agent import reading_prompt_to_use, adjust_feedback_language
import copy

import os

from service.langfuse_client import langfuse

load_dotenv()

langfuse = Langfuse(
    secret_key= os.getenv("LANGFUSE_SECRET_KEY"),
    public_key= os.getenv("LANGFUSE_PUBLIC_KEY"),
    host="https://cloud.langfuse.com"
)

@log_function_io
def answer_feedback_questions( graded_assignment, student_profile, assignment_type, name, use_vision=False, question_number='1', images=[], all_questions=True, description=[],old_assignment=None,context="" ):
    """
    Processes a graded assignment and generates personalized feedback for a student.
    
    Parameters:
        - graded_assignment (list/dict/str): The graded assignment data, which may be a list or dictionary.
        - student_profile (dict): Information about the student, including reading level and grade.
        - assignment_type (str): The type of assignment (e.g., essay, math, history).
        - name (str): The student's name.
        - use_vision (bool, optional): Whether to use vision processing. Defaults to False.
        - question_number (str, optional): The specific question number. Defaults to '1'.
        - images (list, optional): List of images associated with the assignment. Defaults to an empty list.
        - all_questions (bool, optional): Whether to process all questions or just one. Defaults to True.
        - description (list, optional): Additional context or description. Defaults to an empty list.
        - old_assignment (dict, optional): Previous assignment attempt for comparison. Defaults to None.
        - context (str, optional): Additional textual context. Defaults to an empty string.

    Returns:
        - dict/list: The processed assignment with personalized feedback.
    """
    print("Answering feedback questions:", graded_assignment)
    print('old_assignment in answer feedback:',old_assignment)
    if isinstance(graded_assignment, str):
        graded_assignment = ast.literal_eval(graded_assignment)
    if isinstance(graded_assignment, list):
        pass
    else:
        graded_assignment = [graded_assignment]

    # Fetch an example text suitable for the provided reading level
    example_text = reading_level_example(student_profile['reading_level'])

    # If vision, do some preprocessing
    if use_vision:
        graded_assignment = simplify_challenges(graded_assignment, question_number)

    # Determine which tool definition we need
    if all_questions and 'essay' not in assignment_type:
        tool = [
            {
                "type": "function",
                "function": {
                    "name": "summarize_and_merge_feedback",
                    "description": "Summarize and merge repetitive feedback",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "overall_personalized_feedback": {
                                "type": "object",
                                'properties': {
                                    "index": {
                                        "type": "string",
                                        "description": "number of the question"
                                    },
                                    "personalized_feedback": {
                                        "type": "string",
                                        "description": "the feedback for each question for the student using the answers of supporting list of questions"
                                    }
                                },
                                "required": ["index","personalized_feedback"]
                            }
                        },
                        "required": ['overall_personalized_feedback']
                    }
                }
            }
        ]
    else:
        tool = [
            {
                "type": "function",
                "function": {
                    "name": "answer_feedback_questions",
                    "description": "Answer a list of questions based on the graded assignment to provide more detailed and accurate feedback for the student.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "overall_personalized_feedback": {
                                "type": "object",
                                'properties': {
                                    "personalized_feedback": {
                                        "type": "string",
                                        "description": (
                                            "A well-rounded feedback summary that encapsulates the student's performance "
                                            "across the entire assignment, highlighting strengths, understanding of key concepts, "
                                            "and engagement. The feedback is aligned with key rubrics, ensuring a comprehensive "
                                            "evaluation of the student's work."
                                        )
                                    }
                                },
                                "required": ["personalized_feedback"]
                            }
                        },
                        "required": ["overall_personalized_feedback"]
                    }
                }
            }
        ]

    # Pre-calculate the list of questions (used in prompt generation)
    list_of_questions = questions_list(assignment_type)

    # We'll store each question's result (index & personalized_feedback) here
    temp = []

    # --- MULTI-THREADING SECTION BEGINS HERE ---
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Submit each question's processing as a separate task
        future_to_question = {
            executor.submit(process_single_question,question,student_profile,assignment_type,name,use_vision,images,tool,example_text,list_of_questions,description,old_assignment,context): question
            for question in graded_assignment
        }

        # As each future completes, we collect the result
        for future in concurrent.futures.as_completed(future_to_question):
            question = future_to_question[future]
            try:
                response = future.result()
                print(response)
                temp.append(response)
            except Exception as exc:
                print(f"Error processing question {question}: {exc}")
    # --- MULTI-THREADING SECTION ENDS HERE ---

    # Merge the feedback into the graded_assignment
    print("working till here")
    if 'essay' in assignment_type:
        graded_assignment[0]['personalized_feedback']=temp[0]['personalized_feedback']
    else:
        for question in graded_assignment:
            index = question.get('index','1')
            for item in temp:
                if int(index) == int(item['index']):
                    question['personalized_feedback'] = item['personalized_feedback']
    
    if 'essay' in assignment_type:
        graded_assignment=graded_assignment[0]
    print("PROCESSED ASSIGNMENT:", graded_assignment)
    # Return the last response or, if you prefer, the entire graded_assignment
    # to see all the feedback in a single structure.
    return graded_assignment

def old_answer_feedback_questions( graded_assignment, reading_level, assignment_type, name, use_vision=False, question_number='1', images=[], all_questions=True, description=[] ):
    print("Answering feedback questions:", graded_assignment)

    if isinstance(graded_assignment, str):
        graded_assignment = ast.literal_eval(graded_assignment)
    if isinstance(graded_assignment, list):
        pass
    else:
        graded_assignment = [graded_assignment]

    # Fetch an example text suitable for the provided reading level
    example_text = reading_level_example(reading_level)

    # If vision, do some preprocessing
    if use_vision:
        graded_assignment = simplify_challenges(graded_assignment, question_number)

    # Determine which tool definition we need
    if all_questions and 'essay' not in assignment_type:
        tool = [
            {
                "type": "function",
                "function": {
                    "name": "summarize_and_merge_feedback",
                    "description": "Summarize and merge repetitive feedback",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "overall_personalized_feedback": {
                                "type": "object",
                                'properties': {
                                    "index": {
                                        "type": "string",
                                        "description": "number of the question"
                                    },
                                    "personalized_feedback": {
                                        "type": "string",
                                        "description": "the feedback for each question for the student using the answers of supporting list of questions"
                                    }
                                },
                                "required": ["index","personalized_feedback"]
                            }
                        },
                        "required": ['overall_personalized_feedback']
                    }
                }
            }
        ]
    else:
        tool = [
            {
                "type": "function",
                "function": {
                    "name": "answer_feedback_questions",
                    "description": "Answer a list of questions based on the graded assignment to provide more detailed and accurate feedback for the student.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "overall_personalized_feedback": {
                                "type": "object",
                                'properties': {
                                    "personalized_feedback": {
                                        "type": "string",
                                        "description": (
                                            "A well-rounded feedback summary that encapsulates the student's performance "
                                            "across the entire assignment, highlighting strengths, understanding of key concepts, "
                                            "and engagement. The feedback is aligned with key rubrics, ensuring a comprehensive "
                                            "evaluation of the student's work."
                                        )
                                    }
                                },
                                "required": ["personalized_feedback"]
                            }
                        },
                        "required": ["overall_personalized_feedback"]
                    }
                }
            }
        ]

    # Pre-calculate the list of questions (used in prompt generation)
    list_of_questions = questions_list(assignment_type)

    # We'll store each question's result (index & personalized_feedback) here
    temp = []

    # --- MULTI-THREADING SECTION BEGINS HERE ---
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Submit each question's processing as a separate task
        future_to_question = {
            executor.submit(old_process_single_question,question,reading_level,assignment_type,name,use_vision,images,tool,example_text,list_of_questions,description): question
            for question in graded_assignment
        }

        # As each future completes, we collect the result
        for future in concurrent.futures.as_completed(future_to_question):
            question = future_to_question[future]
            try:
                response = future.result()
                print(response)
                temp.append(response)
            except Exception as exc:
                print(f"Error processing question {question}: {exc}")
    # --- MULTI-THREADING SECTION ENDS HERE ---

    # Merge the feedback into the graded_assignment
    print("working till here")
    if 'essay' in assignment_type:
        graded_assignment[0]['personalized_feedback']=temp[0]['personalized_feedback']
    else:
        for question in graded_assignment:
            index = question.get('index','1')
            for item in temp:
                if int(index) == int(item['index']):
                    question['personalized_feedback'] = item['personalized_feedback']
    
    if 'essay' in assignment_type:
        graded_assignment=graded_assignment[0]
    print("PROCESSED ASSIGNMENT:", graded_assignment)
    # Return the last response or, if you prefer, the entire graded_assignment
    # to see all the feedback in a single structure.
    return graded_assignment

def get_feedback_by_grade(grade):
    """

    Retrieves feedback instructions based on the provided grade.

    Parameters:
        - grade (float or str): The student's grade percentage.

    Returns:
        - str: Feedback instructions based on the grade range.
   
    Steps:
      1. If 'grade' is a string, try converting to float.
      2. Clamp the grade between 0 and 100 if needed.
      3. Match the grade to the correct guideline range.
      4. Return ONLY the feedback instructions as a joined string.
    """
    # 1. Convert string to float if needed
    if isinstance(grade, str):
        try:
            grade = float(grade)
        except ValueError:
            raise ValueError("Grade input cannot be converted to a float.")

    # 2. Optional: Clamp grades to the valid range [0, 100]
    if grade < 0:
        grade = 0
    elif grade > 100:
        grade = 100

    # 3. Define feedback guidelines
    guidelines = [
    {
        # 92%-100%
        "min_grade": 92.0,
        "max_grade": 100.0,
        "feedback_instructions": [
            "Tone: Extremely positive and enthusiastic.",
            "Language: Use highly supportive and congratulatory language to highlight exceptional achievements.",
            "Structure: Start with heartfelt praise for their outstanding work, emphasize their mastery of the material, and provide an advanced challenge or extension activity.",
            "Guidance: Encourage exploring leadership opportunities or sharing their knowledge with peers."
        ]
    },
    {
        # 86%-92%
        "min_grade": 86.0,
        "max_grade": 92.0,
        "feedback_instructions": [
            "Tone: Very positive and confident, with gentle encouragement for further growth.",
            "Language: Highlight their strong grasp of the material and commend their effort.",
            "Structure: Open by praising their accomplishments, address small areas for refinement, and provide motivating suggestions to reach the next level.",
            "Guidance: Suggest opportunities to deepen understanding through advanced practice or projects."
        ]
    },
    {
        # 80%-86%
        "min_grade": 80.0,
        "max_grade": 86.0,
        "feedback_instructions": [
            "Tone: Positive and encouraging with a focus on improvement.",
            "Language: Acknowledge their strong performance while gently pointing out areas for growth.",
            "Structure: Start by recognizing their effort, pinpoint one or two areas for enhancement, and conclude with an uplifting message.",
            "Guidance: Recommend specific strategies or additional practice to elevate their work."
        ]
    },
    {
        # 74%-80%
        "min_grade": 74.0,
        "max_grade": 80.0,
        "feedback_instructions": [
            "Tone: Balanced positivity with constructive feedback.",
            "Language: Mix praise for their accomplishments with clear suggestions for refinement.",
            "Structure: Recognize their strengths, identify a few key areas for improvement, and offer actionable steps for growth.",
            "Guidance: Emphasize focusing on consistency and addressing specific challenges."
        ]
    },
    {
        # 68%-74%
        "min_grade": 68.0,
        "max_grade": 74.0,
        "feedback_instructions": [
            "Tone: Constructive and motivational.",
            "Language: Acknowledge their effort while being direct about areas needing attention.",
            "Structure: Highlight successes briefly, address important concepts needing work, and wrap up with encouragement to push forward.",
            "Guidance: Recommend consistent review and practice of specific areas to build confidence."
        ]
    },
    {
        # 62%-68%
        "min_grade": 62.0,
        "max_grade": 68.0,
        "feedback_instructions": [
            "Tone: Supportive but firm about improvement needs.",
            "Language: Use clear, constructive language to address mistakes while maintaining a hopeful tone.",
            "Structure: Mention positive aspects briefly, focus on critical areas for improvement, and provide a structured plan for addressing these issues.",
            "Guidance: Encourage dedication to tackling weaknesses with targeted effort."
        ]
    },
    {
        # 56%-62%
        "min_grade": 56.0,
        "max_grade": 62.0,
        "feedback_instructions": [
            "Tone: Candid yet constructive.",
            "Language: Clearly pinpoint weaknesses while maintaining a tone that encourages persistence.",
            "Structure: Start with acknowledgment of any strengths, focus heavily on areas of failure, and suggest actionable strategies for improvement.",
            "Guidance: Recommend step-by-step remediation and seeking additional resources or help."
        ]
    },
    {
        # 50%-56%
        "min_grade": 50.0,
        "max_grade": 56.0,
        "feedback_instructions": [
            "Tone: Honest and firm, emphasizing the need for significant improvement.",
            "Language: Be direct about critical shortcomings while offering hope and a clear path forward.",
            "Structure: Clearly outline major areas of failure, provide detailed correction strategies, and motivate them to commit to improvement.",
            "Guidance: Stress the importance of seeking support and consistent practice."
        ]
    },
    {
        # Below 50%
        "min_grade": 0.0,
        "max_grade": 50.0,
        "feedback_instructions": [
            "Tone: Very critical and direct, serving as a wake-up call.",
            "Language: Highlight major shortcomings clearly and emphasize the urgency of drastic improvement.",
            "Structure: Point out fundamental failures, outline a detailed and actionable recovery plan, and strongly encourage starting afresh with focused effort.",
            "Guidance: Recommend intensive review of foundational concepts, regular tutoring, and a structured plan to regain confidence and competence."
        ]
    }
]


    # 4. Find matching guideline and return joined instructions
    for guideline in guidelines:
        if guideline["min_grade"] <= grade <= guideline["max_grade"]:
            return " ".join(guideline["feedback_instructions"])

    # Fallback (in case none of the ranges match, though they should)
    return "No guideline found for this grade."

def questions_list(assignment_type):
    list_of_questions=[]
    if 'math' in assignment_type:
        print("math list of questions used")
        list_of_questions.extend(["""LIST of Questions
            1.	Which specific mathematical concept, theorem, or formula did the student misunderstand or misapply?
            This helps identify the exact mathematical principle that the student is struggling with.
            2.	Did the student make a computational error, a procedural mistake, or was the error due to a fundamental misunderstanding of the mathematical concepts involved?
            Helps differentiate between a calculation mistake, a procedural error, or a conceptual misunderstanding.
            3.	Is there a pattern in the student’s errors across multiple math problems, such as consistently misapplying a formula or misunderstanding a type of problem?
            Identifying recurring mathematical errors can point to larger gaps in knowledge or skills. Consider not only the pattern of errors but also the number of correct responses in that pattern to balance the feedback.
            4.	Based on the student's solution, what was their mathematical reasoning or problem-solving approach?
            Analyzing their mathematical thought process reveals their logic and where it diverges from the correct method. Consider both procedural (step-by-step) and conceptual (big-picture) reasoning.
            5.	What key aspect of the problem might the student have overlooked or misinterpreted, such as misreading variables, ignoring constraints, or misinterpreting functions?
            This identifies if the error came from misreading mathematical notation or ignoring crucial problem details.
            6.	Which prior mathematical knowledge or prerequisite skills are needed to address this mistake, such as algebraic manipulation, understanding of functions, or familiarity with geometric properties?
            Points to areas the student might need to revisit or strengthen before attempting similar problems again. Also, consider how recently the student practiced these prerequisite skills to determine if a refresher might be needed.
            7.	What would be a productive hint or next step for the student to reconsider their mathematical approach, such as suggesting they re-express the problem, check calculations, or review relevant theorems?
            Instead of giving the answer, the model could nudge the student toward rethinking their method.
            8.	How did the student’s solution compare to common misconceptions or typical errors in this area of mathematics, like misapplying the distributive property or misunderstanding limits in calculus?
            Identifying if the error is a frequent one can help design targeted feedback and strategies. Additionally, include strategies to address these misconceptions or reinforce correct understanding.
            9.	What visual aid (e.g., graph, diagram), worked example, or analogy could help the student understand the correct mathematical approach?
            This explores how the student might best understand the concept based on their learning style.
            10.	What alternative mathematical method or strategy could the student try to solve the problem if their current approach isn’t working, such as using substitution, factoring, or graphical analysis?
            Suggests different mathematical strategies or problem-solving techniques to foster adaptability and deeper understanding. Also, ask the student to reflect on whether they over-rely on one strategy and how effective that method is for them.
            11.	How confident did the student seem in their approach, and how might their confidence affect their performance?
            Understanding confidence levels can help you provide more personalized feedback, as students often make errors when they’re unsure or rushing through problems.
            """])
    elif 'history' in assignment_type:
        print("history list of questions used")
        list_of_questions.extend([
            """
            1. Which specific historical event, concept, or figure did the student misunderstand or misinterpret?
            Helps identify the exact historical element that the student is struggling with.
            """,
            """
            2. How did the student misunderstand or misinterpret this event, concept, or figure?
            Offers insights into the student’s thought process and the nature of the misunderstanding.
            """,
            """
            3. Why do you think the student made this type of error?
            Explores the reasons behind the student’s mistake.
            """,
            """
            4. Is there a pattern in the student’s errors across multiple history questions, such as consistently misunderstanding a particular era, theme, or cause-and-effect relationship?
            Identifies recurring errors that could indicate larger gaps in knowledge or analytical skills.
            """,
            """
            5. What key aspect of the historical question might the student have overlooked or misinterpreted, such as missing the significance of an event, ignoring context, or misattributing causes and effects?
            Identifies whether the error came from misreading the question or overlooking crucial historical details.
            """,
            """
            6. Did the student understand why these aspects were significant to the historical event or topic?
            Evaluates the student’s comprehension of the importance of key details in historical events.
            """,
            """
            7. Which prior historical knowledge or prerequisite understanding is needed to address this mistake, such as familiarity with certain periods, cultures, or historiographical debates?
            Points to areas the student might need to revisit or strengthen before tackling similar topics again.
            """,
            """
            8. What visual aid (e.g., timelines, maps), illustrative example, or analogy could help the student understand the correct historical perspective?
            Explores how different learning aids could assist the student in better grasping the concept.
            """,
            """
            9. How does the student typically respond to visual aids in other contexts?
            Reflects on whether visual tools have been helpful for the student in the past.
            """,
            """
            10. Does the student demonstrate an understanding of the cause-and-effect relationships between historical events?
            Evaluates whether the student grasps the connections between events and their consequences.
            """
        ])
        
        # Specific to history_fact assignments
        if assignment_type == 'history_fact':
            list_of_questions.append(
            """
            11. Did the student make a factual error, an interpretative mistake, or was the error due to a fundamental misunderstanding of the historical context?
            Differentiates between a simple factual mistake, a misinterpretation, or a deeper conceptual misunderstanding.
            """
            )
        
        # Specific to history_critical and history_essay assignments
        if assignment_type in ['history_critical', 'history_essay']:
            list_of_questions.extend([
            """
            11. Based on the student's response, what was their reasoning or perspective on the historical topic?
            Analyzes the student's thought process and where it diverges from accepted historical understanding.
            """,
            """
            12. How does the student's reasoning align or differ from common historical interpretations?
            Encourages reflection on how well the student understands accepted interpretations of historical events.
            """,
            """
            13. What would be a productive hint or next step for the student to reconsider their approach, such as suggesting they review primary sources, examine multiple perspectives, or re-evaluate the evidence?
            Encourages critical thinking by guiding the student towards rethinking their interpretation instead of providing direct answers.
            """,
            """
            14. How did the student’s answer compare to common misconceptions or typical errors in this area of history, like presentism, overgeneralization, or neglecting socioeconomic factors?
            Helps identify if the error is a frequent one and can be used to design targeted feedback.
            """,
            """
            15. What alternative analytical method or approach could the student use to examine the historical question if their current strategy isn’t yielding accurate insights, such as thematic analysis, comparative studies, or considering the impact of different historical forces?
            Suggests new analytical strategies to encourage adaptability and deeper understanding.
            """,
            """
            16. How well did the student contextualize their answer within the larger historical narrative?
            Measures the student’s ability to place their response within the broader context of history.
            """
            ])
        
        # Specific to history_vocab assignments
        if assignment_type == 'history_vocab':
            list_of_questions.extend([
            """
            11. How well did the student understand and use the historical vocabulary presented in the assignment?
            Assesses the student's comprehension and application of key historical terms.
            """,
            """
            12. Did the student correctly interpret the meaning of the historical terms within the context?
            Evaluates the student's ability to understand vocabulary in context.
            """,
            """
            13. Which specific terms did the student struggle with, and how did that affect their overall comprehension of the historical context?
            Identifies problematic vocabulary that may hinder understanding of historical events.
            """
            ])
    else:
        print("ELA list of questions used")
        list_of_questions.extend([
            """
            1. Which specific aspect of language, literature, or writing did the student misunderstand or misapply (e.g., grammar rules, literary devices, writing conventions)?
            Purpose: Identifies the exact area of English studies that the student is struggling with.
            """,
            """
            2. Is there a pattern in the student’s errors across multiple assignments, such as consistently misusing certain grammar rules, struggling with essay structure, or recurring misinterpretations of themes?
            Purpose: Helps identify recurring errors, pointing to larger gaps in knowledge or skills.
            """,
            """
            3. Which prior knowledge or prerequisite skills are needed to address this mistake, such as understanding literary devices, familiarity with essay structures, or mastery of certain grammar rules?
            Purpose: Highlights areas of knowledge the student might need to revisit or strengthen before attempting similar assignments again.
            """,
            """
            4. What would be a productive hint or next step for the student to reconsider their approach, such as suggesting they revisit the text, create an outline, or review grammar guidelines?
            Purpose: Encourages independent thinking and reflection, nudging the student toward rethinking their method.
            """,
            """
            5. How did the student’s response compare to common misconceptions or typical errors in this area of English, like confusing homophones, misusing metaphors, or failing to support arguments with evidence?
            Purpose: Helps the student understand if their error is a common one, making it easier to address with targeted feedback.
            """,
            """
            6. What examples, writing exercises, or analogies could help the student understand the correct approach, such as providing sample essays, demonstrating effective use of literary devices, or offering grammar practice?
            Purpose: Suggests practical ways for the student to improve, based on their learning style and grade level.
            """,
            """
            6. What alternative strategies or methods could the student use to improve their writing or analysis, such as employing different brainstorming techniques, exploring multiple interpretations, or seeking peer feedback?
            Purpose: Encourages flexibility and adaptability by suggesting different approaches to enhance their learning.
            """,
            """
            8. Did the student demonstrate effort and engagement in the assignment, such as attempting revisions, participating in discussions, or using feedback from previous work?
            Purpose: Assesses the student’s overall engagement with the assignment and their willingness to improve.
            """
        ])
        
        # Questions specific to reading comprehension and essay assignments
        if assignment_type in ['reading_comp_gen', 'reading_comp_prov', 'inf_essay', 'arg_essay']:
            list_of_questions.extend([
                """
                9. Did the student make a grammatical error, a structural mistake in their writing, or was the error due to a fundamental misunderstanding of the text or prompt?
                Purpose: Differentiates between technical mistakes, organizational issues, or conceptual misunderstandings.
                """,
                """
                10. Based on the student's writing, what was their thought process or interpretation of the text or prompt?
                Purpose: Analyzes the student’s understanding and where it diverges from expected interpretations.
                """,
                """
                11. What key aspect of the text or writing prompt might the student have overlooked or misinterpreted, such as missing underlying themes, character motivations, or specific instructions?
                Purpose: Identifies if the error came from misreading the prompt or overlooking crucial details.
                """
            ])
        
        # Questions specific to essay assignments
        if assignment_type in ['inf_essay', 'arg_essay']:
            list_of_questions.extend([
                """
                9. Did the student effectively structure their essay according to the informative/argumentative essay conventions?
                Purpose: Evaluates the student's ability to organize their essay appropriately.
                """,
                """
                10. How well did the student support their arguments or points with evidence from the passage?
                Purpose: Assesses the student's use of textual evidence to support their ideas.
                """,
                """
                11. Did the student maintain a clear focus and coherence throughout their essay?
                Purpose: Checks for consistency and clarity in the student's writing.
                """
            ])
        
        # Questions specific to reading comprehension assignments
        if assignment_type in ['reading_comp_gen', 'reading_comp_prov']:
            list_of_questions.extend([
                """
                9. Did the student accurately identify the main idea or theme of the passage?
                Purpose: Evaluates the student's ability to grasp central concepts.
                """,
                """
                10. How well did the student interpret the details and inferences within the passage?
                Purpose: Assesses the student's analytical and inferential reading skills.
                """,
                """
                11. Did the student understand the author's purpose and tone in the passage?
                Purpose: Checks the student's comprehension of the author's intent and attitude.
                """
            ])
        
        # Questions specific to vocabulary assignments
        if assignment_type == 'vocab_fill':
            list_of_questions.extend([
                """
                9. How well did the student understand and apply the vocabulary words in the given context?
                Purpose: Assesses the student's comprehension and application of key vocabulary.
                """,
                """
                10. Did the student correctly interpret the meaning of the vocabulary words within the passage?
                Purpose: Evaluates the student's ability to understand vocabulary in context.
                """,
                """
                11. Which specific vocabulary words did the student struggle with, and how did that affect their overall comprehension of the passage?
                Purpose: Identifies problematic vocabulary that may hinder understanding.
                """
            ])
    return list_of_questions


def process_single_question( question, student_profile, assignment_type, name, use_vision, images, tool, example_text, list_of_questions,description=[],old_assignment=None,context='' ):
    """
    Function that encapsulates the logic to process a single question
    and returns the 'response' dict which has index & personalized_feedback.

    Error Handling:
    ---------------
    1. Validates that 'question' is a dictionary and contains
       required keys like 'grade_percentage' and 'index'.
    2. Wraps calls to external functions (make_openai_request_with_tools
       or make_openai_vision_request) in try-except blocks to capture
       unexpected errors.
    3. Ensures that the response contains an 'overall_personalized_feedback'
       key before accessing it.
    4. Checks for index mismatches and corrects them if necessary.
    5. Raises ValueError or other appropriate exceptions if data is
       missing or invalid.
    """
    reading_level=student_profile['reading_level']
    grade=student_profile['grade']
    # --- Validate the 'question' argument ---
    if not isinstance(question, dict):
        raise ValueError("'question' must be a dictionary.")

    required_keys = ['grade_percentage'] #uncomment
    for key in required_keys:
        if key not in question:
            raise ValueError(f"Missing required key in 'question': '{key}'")

    # Attempt to retrieve the grade_percentage safely
    try:
        grade_percentage = question['grade_percentage'] #uncomment
        grade_percentage = question['grade']
    except KeyError as e:
        raise ValueError("Missing 'grade_percentage' in question.") from e

    # Attempt to retrieve the index safely
    try:
        if 'essay' not in assignment_type:
            question_index = question['index']
    except KeyError as e:
        raise ValueError("Missing 'index' in question.") from e

    # Attempt to get the feedback guideline
    try:
        feedback_guideline = get_feedback_by_grade(grade_percentage)
    except Exception as e:
        # You could choose to handle different exceptions differently;
        # here we raise a generic error for demonstration.
        raise RuntimeError(
            f"An error occurred while retrieving feedback guideline: {e}"
        ) from e
    temp=question.copy()
    #print('type temp:',type(temp))
    if 'rubrics' in temp:
        temp.pop('rubrics')
    if 'index' in temp:
        temp.pop('index')
    try:
        questions_yes,questions_no=split_rubric_responses(temp)
        #print('questions_no')
        updated_tool=update_tools_structure(questions_yes,questions_no)
    except Exception as e:
        print("failed to adjust tools: question:", temp)
        print("failed to adjust tools: error", e)
    prompt = f"""
    You are a grade {grade} student, and your job is to provide feedback for your friend {name} in the language of a grade {grade} student.

    Your friend {name} has completed an assignment, and you need to give them helpful feedback on one of their answers. Use a friendly and supportive tone like you're talking to a classmate.

    Here are some important things to remember when giving feedback:
    • Keep each point short, clear, and no longer than 100 characters.
    • Each point should be one complete sentence.
    • Avoid repeating the same ideas or using vague or overly generic statements.

    Use these feedback guidelines to help shape your response:
    {feedback_guideline}

    Your feedback should match your friend's reading level. Here’s an example of text that fits the reading level '{reading_level}':
    "{example_text}"

    Make sure that:
    • You explain things in a way that makes sense to a grade {grade} student.
    • You do NOT give away the correct answer, but you help your friend understand their mistake.
    • You point out what {name} did well and why it’s correct. Use specific details from their answer.
    • You also point out any mistakes and explain why they are wrong. Use details from {name}'s answer to help.
    • You can use fun examples or comparisons if it helps your friend understand better.
    • Each feedback point should start with '- ' to keep it easy to read.
    • Make sure your feedback is direct, friendly, and helps {name} feel encouraged to improve.

    Structure your feedback like this:
    - **Praise Statement**: Start by telling {name} something they did well and why it was a good answer.
    - **Constructive Feedback**: For any mistakes, give clear and kind suggestions on how to fix them.
   
    IMPORTANT: Your feedback should be clear and direct, breaking down any tricky parts into smaller steps. Make sure {name} knows exactly what to do to improve.
"""
    if int(float(question['grade_percentage']))==100:
        prompt  += f" - **Encouragement**: End with an encouraging note to keep {name} motivated!"
    else:
        prompt += f" - **Encouragement**: End with an encouraging note to keep {name} motivated!, remind them they can improve and try again."

    if old_assignment:

        old_question = next((item for item in old_assignment if str(item.get('question_number')) == str(question['question_number'])), {})
        # prompt += f"""

        # This is the student's second attempt at solving this question. In your feedback, the praise statement should refer to their previous attempt and acknowledge their improvement by comparing their new response with their old one.

        # Previous attempt:

        # - **Grade Percentage:** {old_question.get('grade_percentage', '')}%
        # - **Student's Previous Answer:** "{old_question.get('students_answer', '')}"
        # - **Previous Feedback:** "{old_question.get('personalized_feedback', '')}"
        # - **Previous Rubric Responses:** {old_question.get('rubrics_responses', '')}


        # Ensure that:
        # • As the first statement in the feedback, acknowledge the student's effort in improving their answer. Highlight the specific areas where they have shown improvement.
        # • The positive statement should be specific to the improvements made in the new response, show a clear comparison with the previous attempt.
        # • Identify and highlight the specific improvements in their response.
        # • If there is still room for improvement, suggest how they can further refine their answer.
        # """
        prompt += f"""

    This is {name}'s second attempt at solving this question. Since you're their friend, start by cheering them on for trying again! Let them know you see their effort and compare their new answer with their last one.

    Here’s what they did before:

    - **Grade Percentage (Last Attempt):** {old_question.get('grade_percentage', '')}%
    - **Their Previous Answer:** "{old_question.get('students_answer', '')}"
    - **Feedback They Got Last Time:** "{old_question.get('personalized_feedback', '')}"
    - **Previous Rubric Responses:** {old_question.get('rubrics_responses', '')}

    When giving feedback this time, make sure to:
    • Start by recognizing {name}'s effort in improving their answer—be specific about what got better!
    • Compare their new response with the old one and highlight exactly what they’ve improved.
    • If they still need to work on something, give friendly and clear advice on how they can make it even better.
    • Keep the feedback short, clear, and easy to understand—just like you would if you were talking to a classmate.

    Keep it positive and encouraging! {name} should feel good about what they improved and know exactly what to do next.
    """

    else:
        print("old_assignment:",old_assignment)
    user_prompt=f"""Here is the Graded assignment: {temp}. "
                Please refer to the feedback guidelines for deciding the tone and language of the feedback.
                Ensure clarity, and direct relevance to the student's work."""
    if context:
        user_prompt += f"This is the passage of the assignment:{context}"
    if questions_no['rubrics_responses']:
        user_prompt += f"These are all the questions in rubrics_responses that student did incorrectly:{questions_no['rubrics_responses']}. make sure you generate 1 critical feedback sentence for each of these questions"
    
    message = [
        {"role": "system", "content": prompt},
        {
            "role": "user",
            "content": user_prompt
        }
    ]


    # --- Call the appropriate API request function ---
    try:
        if use_vision:
            new_feedback = make_openai_vision_request(message, updated_tool, 0.7, images,description)
        else:
            new_feedback = make_openai_request_with_tools(message, updated_tool, 0.7)
    except Exception as e:
        # Handle any errors that might occur during the API request
        raise RuntimeError(
            f"An error occurred while requesting feedback from the API: {e}"
        ) from e

    # --- Validate new_feedback and extract the feedback content ---
    # if not new_feedback or 'overall_personalized_feedback' not in new_feedback:
    #     raise ValueError(
    #         "The API response is missing 'overall_personalized_feedback'. "
    #         "Please ensure the API returns the correct structure."
    #     )
    print("new feedback:",new_feedback)
    #response = new_feedback['overall_personalized_feedback']
    #adding reading level adjustment here
    response={}
    response['personalized_feedback']=organize_feedback(new_feedback)
    if 'index' in question:
        response['index']=question['index']
    # grade_level=int(kincaid_grade_level_test_english(response['personalized_feedback']))
    # #log_function_call([json_response],grade_level,"kincaid_grade_level_test",'')    
    # if type(reading_level)==str:
    #     reading_level_int = extract_and_convert_grade(reading_level)
    # elif type(reading_level)==int:
    #     reading_level_int=reading_level
    # print("reading level adjusted:",reading_level_int)
    # extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
    # print("Before reading level:",grade_level)
    # temp_response=''
    # if extra_prompt:
    #     for i in range(3):
    #         print(f"Processing {i}")
    #         temp_response=adjust_feedback_language(response,extra_prompt,reading_level,name)
    #         print(f"fixing reading-level-{i}:{temp_response}")
    #         grade_level=int(kincaid_grade_level_test_english(temp_response['personalized_feedback']))
    #         print("After reading level:",grade_level)
    #         extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
    #         if not extra_prompt:
    #             print("No more adjustment needed breaking")
    #             break
    # if temp_response:
    #     response=temp_response
    #reading level adjustment end
    print(assignment_type)
    # --- Validate 'response' structure and handle index mismatch ---
    if not isinstance(response, dict):
        raise ValueError("The 'overall_personalized_feedback' must be a dictionary.")
    print("testing if working till here")
    if 'essay' not in assignment_type:
        print("non-essay assignment found")
        if 'index' in response:
            index_from_response = response['index']
            # If there's a mismatch, fix it
            if str(question_index) != str(index_from_response):
                print("INDEX MISMATCH FOUND")
                print("INDEX IN INPUT:", question_index, "INDEX IN RESPONSE:", index_from_response)
                response['index'] = question_index

    return response

def old_process_single_question( question, reading_level, assignment_type, name, use_vision, images, tool, example_text, list_of_questions,description=[] ):
    """
    Function that encapsulates the logic to process a single question
    and returns the 'response' dict which has index & personalized_feedback.

    Error Handling:
    ---------------
    1. Validates that 'question' is a dictionary and contains
       required keys like 'grade_percentage' and 'index'.
    2. Wraps calls to external functions (make_openai_request_with_tools
       or make_openai_vision_request) in try-except blocks to capture
       unexpected errors.
    3. Ensures that the response contains an 'overall_personalized_feedback'
       key before accessing it.
    4. Checks for index mismatches and corrects them if necessary.
    5. Raises ValueError or other appropriate exceptions if data is
       missing or invalid.
    """

    # --- Validate the 'question' argument ---
    if not isinstance(question, dict):
        raise ValueError("'question' must be a dictionary.")

    required_keys = ['grade_percentage'] #uncomment
    for key in required_keys:
        if key not in question:
            raise ValueError(f"Missing required key in 'question': '{key}'")

    # Attempt to retrieve the grade_percentage safely
    try:
        grade_percentage = question['grade_percentage'] #uncomment
        grade_percentage = question['grade']
    except KeyError as e:
        raise ValueError("Missing 'grade_percentage' in question.") from e

    # Attempt to retrieve the index safely
    try:
        if 'essay' not in assignment_type:
            question_index = question['index']
    except KeyError as e:
        raise ValueError("Missing 'index' in question.") from e

    # Attempt to get the feedback guideline
    try:
        feedback_guideline = get_feedback_by_grade(grade_percentage)
    except Exception as e:
        # You could choose to handle different exceptions differently;
        # here we raise a generic error for demonstration.
        raise RuntimeError(
            f"An error occurred while retrieving feedback guideline: {e}"
        ) from e


    prompt = f"""
    I have a graded assignment for a student named {name}. I want concise, focused, and personalized feedback about this one question.

    You have a list of guiding questions to inform your feedback, but your final feedback must:
    • Contain exactly three bullet points, and each point must be strictly one sentence.
    • Each bullet point should be concise, clear, and no longer than 100 characters.
    • Avoid repetition, ambiguity, and overly generic statements.
    • Focus only on this specific question, highlighting strengths and areas to improve.

    Use these questions internally to understand the student's strengths, weaknesses, and areas for improvement:
    {list_of_questions}

    Follow these feedback guidelines:
    {feedback_guideline}

    Match the feedback to the student's reading level. Here is an example of text suitable for the reading level '{reading_level}':
    "{example_text}"

    Ensure that:
    • The feedback uses varied language and is appropriate for {reading_level}.
    • It aligns with {name}'s grade and maintains a supportive tone.
    • The feedback does not disclose the correct answer but helps {name} understand the mistake.
    • Use scaffolded feedback to guide {name} in correcting their work.
    • Explicitly mention the 'resubmit feature' to encourage {name} to try again.
    • Use analogies or examples related to {name}'s interests when appropriate.
    • Specify what {name} solved correctly and why it is correct. Use exact evidence from the students_answer.
    • Specify what {name} solved incorrectly and why it is incorrect. Use exact evidence from the students_answer.
    • Each feedback point must strictly adhere to the three-point limit, with each point being a single sentence to ensure clarity and focus.
    • Provide feedback that identifies strengths, explains the grade, and offers actionable steps to improve.
    • Use '-' as the only character to separate bullet points.
    • The feedback should be addressed directly to the student

    Example of clear and supportive feedback:
    "- Mia, you correctly identified the divisor but missed dividing by 5.  

    - Try drawing 5 groups to visualize the division steps clearly.  

    - Use the resubmit feature to fix this and build confidence in dividing."  

    Your feedback must follow these guidelines and reflect a tone that motivates {name} to continue learning and improving.
    """
    message = [
        {"role": "system", "content": prompt},
        {
            "role": "user",
            "content": (
                f"Here is the Graded assignment: {question}. "
                f"Please refer to the feedback guidelines for deciding the tone and language of the feedback. "
                f"Do not use 'True' or 'False.' Limit your response to exactly three points, each comprising a single sentence. "
                f"Focus on strengths, explain the grade, and offer steps for improvement related to the provided evaluation categories. "
                f"Ensure clarity, positivity, and direct relevance to the student's work."
            )
        }
    ]


    # --- Call the appropriate API request function ---
    try:
        if use_vision:
            new_feedback = make_openai_vision_request(message, tool, 0.7, images,description)
        else:
            new_feedback = make_openai_request_with_tools(message, tool, 0.7)
    except Exception as e:
        # Handle any errors that might occur during the API request
        raise RuntimeError(
            f"An error occurred while requesting feedback from the API: {e}"
        ) from e

    # --- Validate new_feedback and extract the feedback content ---
    if not new_feedback or 'overall_personalized_feedback' not in new_feedback:
        raise ValueError(
            "The API response is missing 'overall_personalized_feedback'. "
            "Please ensure the API returns the correct structure."
        )
    print("new feedback:",new_feedback)
    response = new_feedback['overall_personalized_feedback']
    #adding reading level adjustment here
    grade_level=int(kincaid_grade_level_test_english(response['personalized_feedback']))
    #log_function_call([json_response],grade_level,"kincaid_grade_level_test",'')    
    if type(reading_level)==str:
        reading_level_int = extract_and_convert_grade(reading_level)
    elif type(reading_level)==int:
        reading_level_int=reading_level
    print("reading level adjusted:",reading_level_int)
    extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
    print("Before reading level:",grade_level)
    temp_response=''
    if extra_prompt:
        for i in range(3):
            print(f"Processing {i}")
            temp_response=adjust_feedback_language(response,extra_prompt,reading_level,name)
            print(f"fixing reading-level-{i}:{temp_response}")
            grade_level=int(kincaid_grade_level_test_english(temp_response['personalized_feedback']))
            print("After reading level:",grade_level)
            extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
            if not extra_prompt:
                print("No more adjustment needed breaking")
                break
    if temp_response:
        response=temp_response
    #reading level adjustment end
    print(assignment_type)
    # --- Validate 'response' structure and handle index mismatch ---
    if not isinstance(response, dict):
        raise ValueError("The 'overall_personalized_feedback' must be a dictionary.")
    print("testing if working till here")
    if 'essay' not in assignment_type:
        print("non-essay assignment found")
        if 'index' in response:
            index_from_response = response['index']
            # If there's a mismatch, fix it
            if str(question_index) != str(index_from_response):
                print("INDEX MISMATCH FOUND")
                print("INDEX IN INPUT:", question_index, "INDEX IN RESPONSE:", index_from_response)
                response['index'] = question_index

    return response
