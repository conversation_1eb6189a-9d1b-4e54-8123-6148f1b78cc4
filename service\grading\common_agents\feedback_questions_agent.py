from service.openai_utils.gpt_call import make_openai_langfuse
#from service.vision.vision import make_openai_vision_request
from service.utils.supporting_functions import reading_level_example,split_rubric_responses,update_tools_structure,organize_feedback
import ast
import concurrent.futures
import copy
from service.context_pipeline import logger
from service.grading.common_agents.reading_level_agent import reading_prompt_to_use,adjust_feedback_language
from service.grading.common_agents.assess_reading_level_agent import kincaid_grade_level_test_english,extract_and_convert_grade

def answer_feedback_questions( graded_assignment, student_profile, assignment_type,old_assignment=None,context="" ):
    """
    Processes a graded assignment and generates personalized feedback for a student.
    
    Parameters:
        - graded_assignment (list/dict/str): The graded assignment data, which may be a list or dictionary.
        - student_profile (dict): Information about the student, including reading level and grade.
        - assignment_type (str): The type of assignment (e.g., essay, math, history).
        - name (str): The student's name.
        - use_vision (bool, optional): Whether to use vision processing. Defaults to False.
        - question_number (str, optional): The specific question number. Defaults to '1'.
        - images (list, optional): List of images associated with the assignment. Defaults to an empty list.
        - all_questions (bool, optional): Whether to process all questions or just one. Defaults to True.
        - description (list, optional): Additional context or description. Defaults to an empty list.
        - old_assignment (dict, optional): Previous assignment attempt for comparison. Defaults to None.
        - context (str, optional): Additional textual context. Defaults to an empty string.

    Returns:
        - dict/list: The processed assignment with personalized feedback.
    """
    logger.info(f"Answering feedback questions: {graded_assignment}")
    logger.info(f'old_assignment in answer feedback: {old_assignment}')
    try:
        if isinstance(graded_assignment, str):
            graded_assignment = ast.literal_eval(graded_assignment)
        logger.info(f"Successfully processed graded_assignment type conversion")
    except Exception as e:
        logger.error(f"Error processing graded_assignment type conversion: {e}")
        raise

    try:
        example_text = reading_level_example(student_profile.reading_level)
        logger.info(f"Reading level example text: {example_text}")
    except Exception as e:
        logger.error(f"Error getting reading level example: {e}")
        raise

    try:
        temp = []
        logger.info(f"Starting ThreadPoolExecutor for {len(graded_assignment)} questions")
    except Exception as e:
        logger.error(f"Error initializing temp list or getting graded_assignment length: {e}")
        raise
    try:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_to_question = {
                executor.submit(process_single_question,question,student_profile,assignment_type,example_text,old_assignment,context): question
                for question in graded_assignment
            }
            logger.info(f"Successfully created {len(future_to_question)} futures for processing")

            for future in concurrent.futures.as_completed(future_to_question):
                question = future_to_question[future]
                try:
                    response = future.result()
                    logger.info(f"Response from future: {response}")
                    temp.append(response)
                except Exception as exc:
                    logger.info(f"Error processing question {question}: {exc}")
    except Exception as e:
        logger.error(f"Error in ThreadPoolExecutor processing: {e}")
        raise

    logger.info("Working till here - processing completed futures")
    try:
        if 'essay' in assignment_type:
            graded_assignment[0]['personalized_feedback']=temp[0]['personalized_feedback']
        else:
            for question in graded_assignment:
                index = question.index
                for item in temp:
                    if int(index) == int(item['index']):
                        question.personalized_feedback = item['personalized_feedback']
        logger.info("Successfully assigned personalized feedback to questions")
    except Exception as e:
        logger.error(f"Error assigning personalized feedback: {e}")
        raise

    try:
        if 'essay' in assignment_type:
            graded_assignment=graded_assignment[0]
        logger.info(f"PROCESSED ASSIGNMENT: {graded_assignment}")
        return graded_assignment
    except Exception as e:
        logger.error(f"Error finalizing processed assignment: {e}")
        raise

def get_feedback_by_grade(grade):
    """

    Retrieves feedback instructions based on the provided grade.

    Parameters:
        - grade (float or str): The student's grade percentage.

    Returns:
        - str: Feedback instructions based on the grade range.
   
    Steps:
      1. If 'grade' is a string, try converting to float.
      2. Clamp the grade between 0 and 100 if needed.
      3. Match the grade to the correct guideline range.
      4. Return ONLY the feedback instructions as a joined string.
    """
    # 1. Convert string to float if needed
    if isinstance(grade, str):
        try:
            grade = float(grade)
        except ValueError:
            raise ValueError("Grade input cannot be converted to a float.")

    # 2. Optional: Clamp grades to the valid range [0, 100]
    try:
        if grade < 0:
            grade = 0
        elif grade > 100:
            grade = 100
        logger.info(f"Grade clamped to valid range: {grade}")
    except Exception as e:
        logger.error(f"Error clamping grade to valid range: {e}")
        raise

    # 3. Define feedback guidelines
    guidelines = [
    {
        # 92%-100%
        "min_grade": 92.0,
        "max_grade": 100.0,
        "feedback_instructions": [
            "Tone: Extremely positive and enthusiastic.",
            "Language: Use highly supportive and congratulatory language to highlight exceptional achievements.",
            "Structure: Start with heartfelt praise for their outstanding work, emphasize their mastery of the material, and provide an advanced challenge or extension activity.",
            "Guidance: Encourage exploring leadership opportunities or sharing their knowledge with peers."
        ]
    },
    {
        # 86%-92%
        "min_grade": 86.0,
        "max_grade": 92.0,
        "feedback_instructions": [
            "Tone: Very positive and confident, with gentle encouragement for further growth.",
            "Language: Highlight their strong grasp of the material and commend their effort.",
            "Structure: Open by praising their accomplishments, address small areas for refinement, and provide motivating suggestions to reach the next level.",
            "Guidance: Suggest opportunities to deepen understanding through advanced practice or projects."
        ]
    },
    {
        # 80%-86%
        "min_grade": 80.0,
        "max_grade": 86.0,
        "feedback_instructions": [
            "Tone: Positive and encouraging with a focus on improvement.",
            "Language: Acknowledge their strong performance while gently pointing out areas for growth.",
            "Structure: Start by recognizing their effort, pinpoint one or two areas for enhancement, and conclude with an uplifting message.",
            "Guidance: Recommend specific strategies or additional practice to elevate their work."
        ]
    },
    {
        # 74%-80%
        "min_grade": 74.0,
        "max_grade": 80.0,
        "feedback_instructions": [
            "Tone: Balanced positivity with constructive feedback.",
            "Language: Mix praise for their accomplishments with clear suggestions for refinement.",
            "Structure: Recognize their strengths, identify a few key areas for improvement, and offer actionable steps for growth.",
            "Guidance: Emphasize focusing on consistency and addressing specific challenges."
        ]
    },
    {
        # 68%-74%
        "min_grade": 68.0,
        "max_grade": 74.0,
        "feedback_instructions": [
            "Tone: Constructive and motivational.",
            "Language: Acknowledge their effort while being direct about areas needing attention.",
            "Structure: Highlight successes briefly, address important concepts needing work, and wrap up with encouragement to push forward.",
            "Guidance: Recommend consistent review and practice of specific areas to build confidence."
        ]
    },
    {
        # 62%-68%
        "min_grade": 62.0,
        "max_grade": 68.0,
        "feedback_instructions": [
            "Tone: Supportive but firm about improvement needs.",
            "Language: Use clear, constructive language to address mistakes while maintaining a hopeful tone.",
            "Structure: Mention positive aspects briefly, focus on critical areas for improvement, and provide a structured plan for addressing these issues.",
            "Guidance: Encourage dedication to tackling weaknesses with targeted effort."
        ]
    },
    {
        # 56%-62%
        "min_grade": 56.0,
        "max_grade": 62.0,
        "feedback_instructions": [
            "Tone: Candid yet constructive.",
            "Language: Clearly pinpoint weaknesses while maintaining a tone that encourages persistence.",
            "Structure: Start with acknowledgment of any strengths, focus heavily on areas of failure, and suggest actionable strategies for improvement.",
            "Guidance: Recommend step-by-step remediation and seeking additional resources or help."
        ]
    },
    {
        # 50%-56%
        "min_grade": 50.0,
        "max_grade": 56.0,
        "feedback_instructions": [
            "Tone: Honest and firm, emphasizing the need for significant improvement.",
            "Language: Be direct about critical shortcomings while offering hope and a clear path forward.",
            "Structure: Clearly outline major areas of failure, provide detailed correction strategies, and motivate them to commit to improvement.",
            "Guidance: Stress the importance of seeking support and consistent practice."
        ]
    },
    {
        # Below 50%
        "min_grade": 0.0,
        "max_grade": 50.0,
        "feedback_instructions": [
            "Tone: Very critical and direct, serving as a wake-up call.",
            "Language: Highlight major shortcomings clearly and emphasize the urgency of drastic improvement.",
            "Structure: Point out fundamental failures, outline a detailed and actionable recovery plan, and strongly encourage starting afresh with focused effort.",
            "Guidance: Recommend intensive review of foundational concepts, regular tutoring, and a structured plan to regain confidence and competence."
        ]
    }
]


    # 4. Find matching guideline and return joined instructions
    try:
        for guideline in guidelines:
            if guideline["min_grade"] <= grade <= guideline["max_grade"]:
                result = " ".join(guideline["feedback_instructions"])
                logger.info(f"Found matching guideline for grade {grade}")
                return result
        logger.info(f"No matching guideline found for grade {grade}")
        return "No guideline found for this grade."
    except Exception as e:
        logger.error(f"Error finding matching guideline for grade {grade}: {e}")
        raise


def process_single_question( question, student_profile, assignment_type, example_text,old_assignment=None,context='' ):
    """
    Function that encapsulates the logic to process a single question
    and returns the 'response' dict which has index & personalized_feedback.

    Error Handling:
    ---------------
    1. Validates that 'question' is a dictionary and contains
       required keys like 'grade_percentage' and 'index'.
    2. Wraps calls to external functions (make_openai_request_with_tools
       or make_openai_vision_request) in try-except blocks to capture
       unexpected errors.
    3. Ensures that the response contains an 'overall_personalized_feedback'
       key before accessing it.
    4. Checks for index mismatches and corrects them if necessary.
    5. Raises ValueError or other appropriate exceptions if data is
       missing or invalid.
    """
    logger.info(f"Processing single question: {question}")
    logger.info(f"Student profile: {student_profile}")
    logger.info(f"Assignment type: {assignment_type}")

    try:
        reading_level=student_profile.reading_level
        grade=student_profile.grade
        name=student_profile.first_name
        logger.info(f"Student details - Reading level: {reading_level}, Grade: {grade}, Name: {name}")
    except Exception as e:
        logger.error(f"Error extracting student profile details: {e}")
        raise

    try:
        grade_percentage = question.grade_percentage
        grade_percentage = question.grade
        logger.info(f"Grade percentage extracted: {grade_percentage}")
    except KeyError as e:
        logger.error(f"Missing 'grade_percentage' in question: {e}")
        raise ValueError("Missing 'grade_percentage' in question.") from e

    # Attempt to retrieve the index safely
    try:
        if 'essay' not in assignment_type:
            question_index = question.index
            logger.info(f"Question index extracted: {question_index}")
    except KeyError as e:
        logger.error(f"Missing 'index' in question: {e}")
        raise ValueError("Missing 'index' in question.") from e

    # Attempt to get the feedback guideline
    try:
        feedback_guideline = get_feedback_by_grade(grade_percentage)
        logger.info(f"Feedback guideline retrieved: {feedback_guideline}")
    except Exception as e:
        # You could choose to handle different exceptions differently;
        # here we raise a generic error for demonstration.
        logger.error(f"Error retrieving feedback guideline: {e}")
        raise RuntimeError(
            f"An error occurred while retrieving feedback guideline: {e}"
        ) from e
    try:
        temp=question.copy(deep=True)
        logger.info(f"Successfully created deep copy of question")
    except Exception as e:
        logger.error(f"Error creating deep copy of question: {e}")
        raise
    try:
        questions_yes,questions_no=split_rubric_responses(temp.dict())
        #logger.info('questions_no')
        updated_tool=update_tools_structure(questions_yes,questions_no)
    except Exception as e:
        logger.info(f"failed to adjust tools: question: {temp}")
        logger.info(f"failed to adjust tools: error: {e}")
    try:
        prompt = f"""
    You are a grade {grade} student, and your job is to provide feedback for your friend {name} in the language of a grade {grade} student.

    Your friend {name} has completed an assignment, and you need to give them helpful feedback on one of their answers. Use a friendly and supportive tone like you're talking to a classmate.

    Here are some important things to remember when giving feedback:
    • Keep each point short, clear, and no longer than 100 characters.
    • Each point should be one complete sentence.
    • Avoid repeating the same ideas or using vague or overly generic statements.

    Use these feedback guidelines to help shape your response:
    {feedback_guideline}

    Your feedback should match your friend's reading level. Here’s an example of text that fits the reading level '{reading_level}':
    "{example_text}"

    Make sure that:
    • You explain things in a way that makes sense to a grade {grade} student.
    • You do NOT give away the correct answer, but you help your friend understand their mistake.
    • You point out what {name} did well and why it’s correct. Use specific details from their answer.
    • You also point out any mistakes and explain why they are wrong. Use details from {name}'s answer to help.
    • You can use fun examples or comparisons if it helps your friend understand better.
    • Each feedback point should start with '- ' to keep it easy to read.
    • Make sure your feedback is direct, friendly, and helps {name} feel encouraged to improve.

    Structure your feedback like this:
    - **Praise Statement**: Start by telling {name} something they did well and why it was a good answer.
    - **Constructive Feedback**: For any mistakes, give clear and kind suggestions on how to fix them.
   
    IMPORTANT: Your feedback should be clear and direct, breaking down any tricky parts into smaller steps. Make sure {name} knows exactly what to do to improve.
"""
        if int(float(question.grade_percentage))==100:
            prompt  += f" - **Encouragement**: End with an encouraging note to keep {name} motivated!"
        else:
            prompt += f" - **Encouragement**: End with an encouraging note to keep {name} motivated!, remind them they can improve and try again."

        if old_assignment:
            old_question = next((item for item in old_assignment if str(item.get('question_number')) == str(question.question_number)), {})
        # prompt += f"""

        # This is the student's second attempt at solving this question. In your feedback, the praise statement should refer to their previous attempt and acknowledge their improvement by comparing their new response with their old one.

        # Previous attempt:

        # - **Grade Percentage:** {old_question.get('grade_percentage', '')}%
        # - **Student's Previous Answer:** "{old_question.get('students_answer', '')}"
        # - **Previous Feedback:** "{old_question.get('personalized_feedback', '')}"
        # - **Previous Rubric Responses:** {old_question.get('rubrics_responses', '')}


        # Ensure that:
        # • As the first statement in the feedback, acknowledge the student's effort in improving their answer. Highlight the specific areas where they have shown improvement.
        # • The positive statement should be specific to the improvements made in the new response, show a clear comparison with the previous attempt.
        # • Identify and highlight the specific improvements in their response.
        # • If there is still room for improvement, suggest how they can further refine their answer.
        # """
            prompt += f"""

        This is {name}'s second attempt at solving this question. Since you're their friend, start by cheering them on for trying again! Let them know you see their effort and compare their new answer with their last one.

        Here’s what they did before:

        - **Grade Percentage (Last Attempt):** {old_question.get('grade_percentage', '')}%
        - **Their Previous Answer:** "{old_question.get('students_answer', '')}"
        - **Feedback They Got Last Time:** "{old_question.get('personalized_feedback', '')}"
        - **Previous Rubric Responses:** {old_question.get('rubrics_responses', '')}

        When giving feedback this time, make sure to:
        • Start by recognizing {name}'s effort in improving their answer—be specific about what got better!
        • Compare their new response with the old one and highlight exactly what they’ve improved.
        • If they still need to work on something, give friendly and clear advice on how they can make it even better.
        • Keep the feedback short, clear, and easy to understand—just like you would if you were talking to a classmate.

        Keep it positive and encouraging! {name} should feel good about what they improved and know exactly what to do next.
        """

        else:
            logger.info(f"old_assignment: {old_assignment}")

        user_prompt=f"""Here is the Graded assignment: {temp}. "
                    Please refer to the feedback guidelines for deciding the tone and language of the feedback.
                    Ensure clarity, and direct relevance to the student's work."""
        if context:
            user_prompt += f"This is the passage of the assignment:{context}"
        if questions_no['rubrics_responses']:
            user_prompt += f"These are all the questions in rubrics_responses that student did incorrectly:{questions_no['rubrics_responses']}. make sure you generate 1 critical feedback sentence for each of these questions"

        message = [
            {"role": "system", "content": prompt},
            {
                "role": "user",
                "content": user_prompt
            }
        ]
        logger.info(f"Successfully created prompt and message for feedback generation")
    except Exception as e:
        logger.error(f"Error creating prompt and message: {e}")
        raise

    try:
        new_feedback = make_openai_langfuse(message, updated_tool, temperature=0.7)
    except Exception as e:
        raise RuntimeError(
            f"An error occurred while requesting feedback from the API: {e}"
        ) from e


    logger.info(f"new feedback: {new_feedback}")
    try:
        response={}
        response['personalized_feedback']=organize_feedback(new_feedback)
        if hasattr(question,'index'):
            response['index']=question.index
        logger.info(f"Successfully created response dictionary")
    except Exception as e:
        logger.error(f"Error creating response dictionary: {e}")
        raise
    try:
        grade_level=int(kincaid_grade_level_test_english(response['personalized_feedback']))
        #log_function_call([json_response],grade_level,"kincaid_grade_level_test",'')    
        if type(reading_level)==str:
            reading_level_int = extract_and_convert_grade(reading_level)
        elif type(reading_level)==int:
            reading_level_int=reading_level
        print("reading level adjusted:",reading_level_int)
        extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
        print("Before reading level:",grade_level)
        temp_response=''
        if extra_prompt:
            for i in range(3):
                print(f"Processing {i}")
                temp_response=adjust_feedback_language(response,extra_prompt,reading_level,name)
                print(f"fixing reading-level-{i}:{temp_response}")
                grade_level=int(kincaid_grade_level_test_english(temp_response['personalized_feedback']))
                print("After reading level:",grade_level)
                extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
                if not extra_prompt:
                    print("No more adjustment needed breaking")
                    break
        if temp_response:
            logger.info(f"Successfully adjusted reading level:{temp_response}")
            response=temp_response

    except Exception as e:
        logger.error(f"Error adjusting reading level: {e}")
        raise
    #reading level adjustment end
    
    try:
        if 'essay' not in assignment_type:
            logger.info("Non-essay assignment found")
            if 'index' in response:
                index_from_response = response['index']
                # If there's a mismatch, fix it
                if str(question_index) != str(index_from_response):
                    logger.info("INDEX MISMATCH FOUND")
                    logger.info(f"INDEX IN INPUT: {question_index}, INDEX IN RESPONSE: {index_from_response}")
                    response['index'] = question_index
        logger.info(f"Successfully processed index validation")
    except Exception as e:
        logger.error(f"Error processing index validation: {e}")
        raise

    try:
        logger.info(f"Returning final response: {response}")
        return response
    except Exception as e:
        logger.error(f"Error returning response: {e}")
        raise