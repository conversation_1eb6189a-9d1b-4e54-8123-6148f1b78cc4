
from service.utils.supporting_functions import reading_level_example
from service.openai_utils.gpt_call import make_openai_langfuse
from service.langfuse_client import langfuse

def summarize_personalized_feedback(student,assignment,assignment_type):
    print("inside summarize personalized_feedback")
    print(student)
    print(assignment)
    reading_example=reading_level_example(student.reading_level)

    if 'history' in assignment_type:
        summarize_personalized_feedback = langfuse.get_prompt("summarize_personalized_feedback_history", label="latest")
    else:
        summarize_personalized_feedback = langfuse.get_prompt("summarize_personalized_feedback", label="latest")
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "summarize_personalize_feedback",
                "description": "Generate an overall personalized feedback summary based on the individual feedback provided for each question in an assignment.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "overall_personalized_feedback": {
                            "type": "string",
                            "description": "A well-rounded feedback summary that encapsulates the student's performance across the entire assignment, highlighting strengths, understanding of key concepts, and engagement. The feedback is aligned with key rubrics, ensuring a comprehensive evaluation of the student's work."

                        },
                    },
                    "required": ["overall_personalized_feedback"]
                }
            }
        }
    ]
    temperature = summarize_personalized_feedback.config.get("openai", {}).get("temperature",0.5)
    model = summarize_personalized_feedback.config.get("openai", {}).get("model","gpt-4.1-nano")
    max_tokens = summarize_personalized_feedback.config.get("openai", {}).get("max_tokens",4096)
    complete_chat_prompt = summarize_personalized_feedback.compile(
        assignment=assignment,
        student=student,
        reading_example= reading_example
    )

    # Initialize OpenAI client
    response = make_openai_langfuse(complete_chat_prompt, tools,model,temperature,max_tokens)
    print(response) 
    return response['overall_personalized_feedback']
   