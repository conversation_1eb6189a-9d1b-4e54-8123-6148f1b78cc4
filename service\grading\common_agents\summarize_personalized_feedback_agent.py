
from service.utils.supporting_functions import reading_level_example
from service.utils.gpt_call import make_openai_request_with_tools
from service.llm.llm_service import OpenAIClient
from service.logging import log_function_io

import os

from service.langfuse_client import langfuse

load_dotenv()

langfuse = Langfuse(
    secret_key= os.getenv("LANGFUSE_SECRET_KEY"),
    public_key= os.getenv("LANGFUSE_PUBLIC_KEY"),
    host="https://cloud.langfuse.com"
)

@log_function_io
def summarize_personalized_feedback(student,assignment,assignment_type):
    print("inside summarize personalized_feedback")
    print(student)
    print(assignment)
    reading_example=reading_level_example(student["reading_level"])

    if 'history' in assignment_type:
        summarize_personalized_feedback = langfuse.get_prompt("summarize_personalized_feedback_history", label="latest")
    else:
        summarize_personalized_feedback = langfuse.get_prompt("summarize_personalized_feedback", label="latest")
    
    tools = [
        {
            "type": "function",
            "function": {
                "name": "summarize_personalize_feedback",
                "description": "Generate an overall personalized feedback summary based on the individual feedback provided for each question in an assignment.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "overall_personalized_feedback": {
                            "type": "string",
                            "description": "A well-rounded feedback summary that encapsulates the student's performance across the entire assignment, highlighting strengths, understanding of key concepts, and engagement. The feedback is aligned with key rubrics, ensuring a comprehensive evaluation of the student's work."

                        },
                    },
                    "required": ["overall_personalized_feedback"]
                }
            }
        }
    ]
    
    complete_chat_prompt = summarize_personalized_feedback.compile(
        assignment=assignment,
        student=student,
        reading_example= reading_example
    )
    # Initialize OpenAI client
    openai_client = OpenAIClient(model="gpt-4o", temperature=0.5)

    feedback= openai_client.make_openai_call(
        messages=complete_chat_prompt,
        tools=tools)
    print(feedback) 
    return feedback['overall_personalized_feedback']
   
@log_function_io
def format_rubric_feedback(feedback):
    """
    Format the feedback for each rubric into a string, separated by double line breaks and a dash.

    Args:
    - feedback (dict): The overall personalized feedback dictionary containing feedback for each rubric.

    Returns:
    - str: A formatted string with feedback for each rubric separated by double line breaks and a dash.
    """
    # Ensure feedback contains all required fields
    required_fields = ['grammar', 'vocabulary', 'factual', 'topic', 'engagement']
    for field in required_fields:
        if field not in feedback:
            raise ValueError(f"Missing required feedback field: {field}")

    # Create a formatted string with each rubric's feedback
    formatted_feedback = (
        f"- {feedback['grammar']}"
        f"\n\n - {feedback['vocabulary']}"
        f"\n\n - {feedback['factual']}"
        f"\n\n - {feedback['topic']}"
        f"\n\n - {feedback['engagement']}"
    )
    
    return formatted_feedback

