# reading_comp_gen_flow.py
from typing import Dict, List, Optional, TypedDict
from langgraph.graph import Graph
# from langgraph.checkpoint.sqlite import SqliteSaver
import copy
import logging
from service.common_agents.summarize_personalized_feedback_agent import summarize_personalized_feedback
from service.common_agents.feedback_questions_agent import answer_feedback_questions
from service.common_agents.safeguard import verify_feedback
from service.grading.language.spanish_refinement_agent import translate_assignment
from service.utils.supporting_functions import (
    add_feedback_and_calculate_average,
    calculate_grade_from_questions,
    extract_question_text
)
from service.processing_questions_agents.create_tools import generate_question_json
from service.processing_questions_agents.questions_prompt_template import prompt_template
from service.llm.llm_service import OpenAIClient
import os
from service.langfuse_client import langfuse
os.environ["LANGFUSE_PUBLIC_KEY"] = "******************************************"
os.environ["LANGFUSE_SECRET_KEY"] = "pk-lf-0513ff27-cfe4-4f4d-a17d-732218c93596"
langfuse = Langfuse()

logger = logging.getLogger(__name__)

class ReadingComprehensionGrader:
    def __init__(self):
        self.workflow = self.build_workflow()
        self.llm_client = OpenAIClient(model="gpt-4.1-nano", temperature=0.5)

    def build_workflow(self):
        """Build and compile the LangGraph workflow"""
        workflow = Graph()
        
        # Define nodes
        workflow.add_node("initialize", self.initialize_state)
        workflow.add_node("process_question", self.process_question)
        workflow.add_node("compile_results", self.compile_results)
        workflow.add_node("generate_feedback", self.generate_personalized_feedback)
        workflow.add_node("translate_response", self.handle_translation)
        
        # Define edges
        workflow.add_edge("initialize", "process_question")
        workflow.add_edge("process_question", "compile_results")
        workflow.add_edge("compile_results", "generate_feedback")
        workflow.add_edge("generate_feedback", "translate_response")
        
        # Set entry and exit points
        workflow.set_entry_point("initialize")
        workflow.set_finish_point("translate_response")
        
        # # Compile with checkpointing
        # return workflow.compile(checkpointer=SqliteSaver.from_conn_string(":memory:"))

    def initialize_state(self, state: Dict) -> Dict:
        """Validate inputs and initialize processing state"""
        if not isinstance(state.get('assignment'), list):
            raise ValueError("Assignment must be a list of questions")
        
        if not all(isinstance(q, dict) and 'rubrics' in q for q in state['assignment']):
            raise ValueError("All questions must have rubrics")
        
        return {
            **state,
            'rubric_responses': [],
            'grades': [],
            'feedback': [],
            'current_question': None,
            'final_response': None
        }

    async def process_question(self, state: Dict) -> Dict:
        """Process a single question with rubric"""
        question = state['assignment'].pop(0)
        state['current_question'] = question
        
        try:
            # Prepare prompt and tools
            rubrics = question['rubrics'].copy()
            rubrics_questions = extract_question_text(rubrics)
            prompt = self.build_question_prompt(question, rubrics, state)
            tools = [generate_question_json(rubrics_questions)]
            
            # Call LLM
            response = await self.llm_client.make_openai_call(
                messages=[{"role": "user", "content": prompt}],
                tools=tools
            )
            
            # Process response
            grade = calculate_grade_from_questions(response, 5)
            question_index = int(response['index'])
            
            # Update state
            state['assignment'][question_index-1].update({
                'rubrics_responses': {
                    k: v for k, v in response.items()
                    if k not in ['personalized_feedback', 'index', 'grade']
                },
                'grade_percentage': (grade/5)*100
            })
            state['rubric_responses'].append(response)
            state['grades'].append(grade)
            state['feedback'].append(response["personalized_feedback"])
            
        except Exception as e:
            logger.error(f"Failed to process question {question.get('question_number')}: {str(e)}")
            raise
        
        return state

    def build_question_prompt(self, question: Dict, rubrics: List, state: Dict) -> str:
        """Construct the evaluation prompt for a question"""
        old_question = {}
        if state['old_assignment']:
            old_question = next(
                (q for q in state['old_assignment']
                 if str(q.get('question_number')) == str(question['question_number'])),
                {}
            )
        langfuse_prompt = langfuse.get_prompt('prompt_template')
        prompt = langfuse_prompt.compile(
            dyslexia = "",
            rubrics = extract_question_text(rubrics),
            old_question_prompt = self.build_old_assignment_prompt(old_question),
            feedback_guidelines_prompt = self.build_feedback_guidelines_prompt(state['old_assignment'] is None)
        )
        return prompt

    def compile_results(self, state: Dict) -> Dict:
        """Compile all question results"""
        try:
            updated_list, final_grade = add_feedback_and_calculate_average(
                state['assignment'],
                [{
                    "personalized_feedback": fb,
                    "grade": g,
                    "index": i+1
                } for i, (fb, g) in enumerate(zip(state['feedback'], state['grades']))]
            )
            
            state['final_response'] = {
                "challenges": state['assignment'],
                "overall_grade": final_grade,
                "student_id": state['student_id'],
                "teacher_id": state['teacher_id'],
                "session_id": state['session_id']
            }
            
        except Exception as e:
            logger.error(f"Failed to compile results: {str(e)}")
            raise
        
        return state
    
    def generate_personalized_feedback(state: Dict) -> Dict:
        """Generate and add personalized feedback to the response."""
        try:
            # Generate summarized feedback
            summarized_feedback = summarize_personalized_feedback(
                state['student_profile'],
                state['assignment'],
                state['assignment_type']
            )
            
            # Answer feedback questions
            new_feedback = answer_feedback_questions(
                state['assignment'], state['student_profile'],
                state['assignment_type'], state['student_profile']['name'],
                state['use_vision'], state['question_number'],
                state['images'], old_assignment=state['old_assignment'],
                context=state['context']
            )
            
            if isinstance(new_feedback, str):
                summarized_feedback = new_feedback
            else:
                new_feedback = verify_feedback(new_feedback, state['assignment_type'])
                state['assignment'] = new_feedback
            
            # Add to final response
            state['final_response']['overall_personalized_feedback'] = summarized_feedback
            
        except Exception as e:
            logger.error(f"Error generating feedback: {e}")
            raise
        
        return state

    def handle_translation(state: Dict) -> Dict:
        """Handle translation if needed."""
        if state['language'] == "spanish":
            try:
                state['final_response'] = translate_assignment(
                    state['final_response'], state['student_profile'],
                    state['question_number'], state['assignment_type'],
                    state['language']
                )
            except Exception as e:
                logger.error(f"Error during translation: {e}")
                raise
        
        return state
    

    def build_old_assignment_prompt(old_question: Dict) -> str:
        """Build prompt section for old assignment comparison."""
        return f"""
            The student has attempted this assignment before, and you will be provided with the previous attempt to compare their progress.
            Please provide feedback on how the student's answer has improved and areas that still need improvement.
            Acknowledge any improvements made since the previous attempt and provide guidance on how to further enhance their performance.

            Previous attempt:
            - Grade Percentage: {old_question.get('grade_percentage', "")}%
            - Student's Previous Answer: "{old_question.get('students_answer', "")}"
            - Previous Feedback: "{old_question.get('personalized_feedback', "")}"
            - Previous Rubric Responses: {old_question.get('rubrics_responses', "")}

            Compare this with the current attempt and highlight:
            - Areas where the student has improved.
            - Areas where they still need to improve.
            - Specific feedback on how they can further refine their response.
            
            The Feedback guidelines are as follows:
            - With one statement praise the student for improving their answer based on the previous attempt.
            - Critical Feedback: For each rubric response marked as false, provide a separate constructive statement highlighting the issue and suggesting how the student could improve their response. These are all the questions in rubrics_responses that student did correctly:{old_question.get('rubrics_responses', "")}, so 1 statement for each of these questions
        """

    def build_feedback_guidelines_prompt(include_critical_feedback: bool) -> str:
        """Build standard feedback guidelines prompt."""
        prompt = """Extra feedback guidelines are as follows:
            - With one statement praise the student for questions that were answered correctly and satisfying the rubric questions.
            """
        if include_critical_feedback:
            prompt += """
                - With one statement provide critical feedback for questions that were answered incorrectly or did not satisfy the rubric questions.
                """
        prompt += """   
            - In the third statement:
                Encourage the student to resubmit the assignment to improve their grade.
                Do not provide the correct answers to the questions, but provide guidance on how to improve the answers.
                Provide feedback that is clear, concise, and actionable.                        
                No need to tell the student to resubmit if they have already received a perfect score.
            - No need to praise the student if they failed to answer any of the questions correctly.
            - No need to criticize the student if they answered all the questions correctly.
        """
        return prompt

def flow(assignment, student_profile, assignment_type, student_id, teacher_id, session_id,
         context='', use_vision=False, question_number='', images=[], language="english",
         dyslexia=False, old_assignment=None):
    """Main entry point that maintains the original interface"""
    grader = ReadingComprehensionGrader()
    initial_state = {
        "assignment": copy.deepcopy(assignment),
        "student_profile": student_profile,
        "assignment_type": assignment_type,
        "student_id": student_id,
        "teacher_id": teacher_id,
        "session_id": session_id,
        "context": context,
        "use_vision": use_vision,
        "question_number": question_number,
        "images": images,
        "language": language,
        "dyslexia": dyslexia,
        "old_assignment": old_assignment
    }
    
    try:
        return grader.workflow.invoke(initial_state)['final_response']
    except Exception as e:
        logger.error(f"Grading workflow failed: {str(e)}")
        raise



assignment_sample = {
  "challenges": [
    {
      "answerKeys": "Alex feels free and happy when swimming, like flying.",
      "description": "Think about what 'flying underwater' tells us about Alex's feelings.",
      "index": "1",
      "rubrics": [
        {
          "rubric_question_number": "Q1",
          "rubric_question_text": "Does the student correctly interpret 'flying underwater' as a feeling of freedom and happiness for Alex?"
        },
        {
          "rubric_question_number": "Q2",
          "rubric_question_text": "Does the student provide a response that accurately reflects Alex's emotions as described in the passage?"
        },
        {
          "rubric_question_number": "Q3",
          "rubric_question_text": "Is the student's explanation of 'flying underwater' consistent with the context provided in the passage?"
        },
        {
          "rubric_question_number": "Q4",
          "rubric_question_text": "Does the student use specific details from the passage to support their interpretation of Alex's feelings?"
        },
        {
          "rubric_question_number": "Q5",
          "rubric_question_text": "Does the student demonstrate an understanding of the metaphorical nature of 'flying underwater'?"
        }
      ],
      "task": "What does 'flying underwater' mean about how Alex feels when swimming?",
      "students_answer": "Alex feels free and happy when swimming, like flying."
    },
    {
      "answerKeys": "Coach Rivera told Alex to swim smart. Alex used good moves and swam better. This helped Alex win and set a new record.",
      "description": "Think about how swimming smart helps Alex do well.",
      "index": "2",
      "rubrics": [
        {
          "rubric_question_number": "Q1",
          "rubric_question_text": "Does the student correctly explain how Coach Rivera's advice to 'swim smart' helped Alex improve?"
        },
        {
          "rubric_question_number": "Q2",
          "rubric_question_text": "Does the student's response include specific examples from the passage to support their answer?"
        },
        {
          "rubric_question_number": "Q3",
          "rubric_question_text": "Does the student accurately describe the impact of Coach Rivera's guidance on Alex's performance in the race?"
        },
        {
          "rubric_question_number": "Q4",
          "rubric_question_text": "Does the student demonstrate an understanding of the passage's main idea about the importance of technique over speed?"
        },
        {
          "rubric_question_number": "Q5",
          "rubric_question_text": "Does the student identify the role of practice and preparation in Alex's success?"
        }
      ],
      "task": "How does Coach Rivera help Alex swim better at the big race?",
      "students_answer": "Coach Rivera told Alex to swim smart. Alex used good moves and swam better. This helped Alex win and set a new record."
    }
  ],
  "conclusion": "Through Alex's journey, we learn that dedication and focus can lead to success in achieving our dreams.",
  "estimatedCompletionTime": "20 minutes",
  "instructions": [
    "Read the passage carefully.",
    "Answer the questions using complete sentences.",
    "Think about how Alex's story can inspire you in your activities."
  ],
  "introduction": "This reading comprehension assignment will take you on a journey with Alex, who loves swimming and dreams of setting records. Follow Alex's story as Alex prepares for a big swimming championship.",
  "language": "english",
  "passage": "Alex loved swimming because it felt like flying underwater. At Splash Aquatic Center, Alex was part of the junior swimming team. The center was well-known for hosting big swimming events.\\n\\nOne day, Coach Rivera gathered everyone by the pool. 'Great news! Next month, we'll host the Junior Swim Championship,' Coach Rivera said. 'This is your chance to shine and maybe set new records!'\\n\\nAlex was thrilled. Alex admired Michael Phelps, who set many world records at the Olympics. Alex dreamed of seeing Alex's name on the record board, just like Michael Phelps.\\n\\nDuring practice, Coach Rivera said, 'Focus on technique, not just speed. Swim smart!'\\n\\nAlex practiced a lot, working on Alex's strokes to swim smoothly. Alex kept a journal to track Alex's progress and improvements.\\n\\nThe championship day came, and the pool was full of excitement. Swimmers warmed up while the crowd cheered.\\n\\nAlex's event, the 100-meter freestyle, was called. Alex stood on the starting block, took a deep breath, and remembered all Alex's hard work.\\n\\nWhen the whistle blew, Alex dove in, swimming with precision. Reaching the final lap, Alex pushed hard to beat Alex's personal best.\\n\\nAlex touched the wall and checked the scoreboard. Alex had beaten Alex's record and set a new team record!\\n\\nThe crowd cheered loudly, and Coach Rivera gave Alex a thumbs up. Alex felt so proud and learned that with hard work and focus, Alex could achieve great things in swimming.",
  "reflection": "How do you think keeping a journal helped Alex improve in swimming?",
  "title": "Swimming to Success"
}

student_profile_sample = {
        "state_standard": "Reading comprehension",
        "topic": "Reading comprehension",
        "grade": "5th",
        "reading_level": "5th",
        "name": "Alex"
    }

# Example test call
result = flow(
    assignment=assignment_sample,
    student_profile=student_profile_sample,
    assignment_type="reading_comp_gen",
    student_id="123",
    teacher_id="456",
    session_id="test-session"
)

logging.info(f"Grading result: {result}")