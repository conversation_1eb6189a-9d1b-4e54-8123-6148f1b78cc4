# agents_registry.py
from service.rubrics.new_york_next_generation_math_standards import final_answer, method
from service.rubrics.new_york_state_standard import content_and_analysis,coherence_organization_and_style,command_of_evidence,constructed_response,control_of_conventions
from service.rubrics.peekskill_city_school_district import content_and_analysis_peekskill,structure_and_completeness,language_and_readability
AGENTS = {
    "new york next generation math standards": {
        "final_answer": final_answer.agent,
        "method": method.agent,
    },
    "new york state standard": {
        "content_and_analysis":content_and_analysis.agent,
        "coherence_organization_and_style":coherence_organization_and_style.agent,
        "command_of_evidence":command_of_evidence.agent,
        "constructed_response":constructed_response.agent,
        "control_of_conventions": control_of_conventions.agent
    },
    "peekskill city school district":{
        "content_and_analysis_peekskill": content_and_analysis_peekskill.agent,
        "language_and_readability": language_and_readability.agent,
        "structure_and_completeness": structure_and_completeness.agent,
    }
}