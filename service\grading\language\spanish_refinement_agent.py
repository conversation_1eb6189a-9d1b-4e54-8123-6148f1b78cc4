from service.utils.gpt_call import make_openai_request_with_tools, make_simple_openai_request
from service.grading.language.spanish_wordlists import spanish_vocab_example


def translate_assignment(graded_assignment, student,question_number, assignment_type, language="english"):
    """
    This function translates a given assignment into the specified language, ensuring the translation is accurate and contextually appropriate.
    It maintains the original meaning and educational purpose of the assignment while adapting it to make sense in the target language.

    Parameters:
    graded_assignment (dict): The graded assignment in JSON format.
    student (dict): The student profile containing information such as grade level.
    question_number (str): The number of questions in the assignment.
    assignment_type (str): The type of assignment being translated.
    language (str): The target language to translate the assignment into.

    Returns:
    dict: The translated assignment in the specified language
    """
    print("Translating Grading:",graded_assignment)

    system_prompt = f"""
    Translate the following feedback into {language}, ensuring that the translation is accurate and contextually appropriate.
    Make sure the feedback retains the same meaning and tone as the original assignment, but is translated into {language} if not already in {language}.
    Ensure that each feedback statement is separated by the new paragraph operator "\n\n".
    The response should only contain the translated feedback, without any additional text or commentary.
    """

    messages = [
        {"role": "system", "content": system_prompt},
    ]
    if "essay" in assignment_type:
        graded_assignment_challenges = graded_assignment['challenges']
        feedback = graded_assignment_challenges['personalized_feedback']
        messages.append({"role": "user", "content": f" This is the feedback: {feedback}"})
        print("Feedback:",feedback)
        print("Messages:",messages)
        feedback = make_simple_openai_request(messages, 0.2)
        graded_assignment_challenges['personalized_feedback'] = feedback
        
        if student['state_standard'] == "New York State Standard":
            agents = ["command_of_evidence", "coherence_organization_and_style", "content_and_analysis", "control_of_conventions"]
            for agent in agents:
                feedback = graded_assignment_challenges[f'{agent}_personalized_feedback']
                messages.pop()
                messages.append({"role": "user", "content": f" This is the feedback: {feedback}"})
                feedback = make_simple_openai_request(messages, 0.2)
                graded_assignment_challenges[f'{agent}_personalized_feedback'] = feedback
        graded_assignment['challenges'] = graded_assignment_challenges

        messages.pop()
        if assignment_type.lower() in ['inf_essay','arg_essay']:
            overall_personalized_feedback = graded_assignment['personalized_feedback']
        else:
            overall_personalized_feedback=graded_assignment['overall_personalized_feedback']
        messages.append({"role": "user", "content": f"This is the feedback: {overall_personalized_feedback}"})
        overall_personalized_feedback = make_simple_openai_request(messages, 0.2)
        if assignment_type.lower() in ['inf_essay','arg_essay']:
            graded_assignment['personalized_feedback'] = overall_personalized_feedback
        else:
            graded_assignment['overall_personalized_feedback'] = overall_personalized_feedback    
    else:
        overall_personalized_feedback = graded_assignment['overall_personalized_feedback']
        messages.append({"role": "user", "content": f"This is the feedback: {overall_personalized_feedback}"})
        overall_personalized_feedback = make_simple_openai_request(messages, 0.2)
        print("Overall Personalized Feedback:",overall_personalized_feedback)
        graded_assignment['overall_personalized_feedback'] = overall_personalized_feedback
        
        graded_assignment_challenges = graded_assignment['challenges']
        print("Graded Assignment:",graded_assignment)
        feedback_list = []


        for question in graded_assignment_challenges:
            feedback_list.append({'index':question['index'],'personalized_feedback':question['personalized_feedback']})

        for feedback in feedback_list:
            messages.pop()
            messages.append({"role": "user", "content": f"This is the feedback: {feedback['personalized_feedback']}"})
            print("Feedback:",feedback)
            print("Messages:",messages)
            feedback['personalized_feedback'] = make_simple_openai_request(messages, 0.2)

        for question in graded_assignment_challenges:
            try:
                question['personalized_feedback'] = feedback_list[int(question['index'])-1]['personalized_feedback']
            except:
                question['personalized_feedback'] = feedback_list[0]['personalized_feedback']

        graded_assignment['challenges'] = graded_assignment_challenges

    print("Graded Assignment:",graded_assignment)

    return graded_assignment

def vocab_simplification_agent(graded_assignment, student,question_number, assignment_type):
    """
    This function simplifies the vocabulary in a Spanish assignment, replacing advanced words with simpler alternatives suitable for the given grade level.
    It ensures the context and meaning of the original assignment remain unchanged while making it more accessible to students at the specified grade.

    Parameters:
    graded_assignment (dict): The graded Spanish assignment in JSON format.
    student (dict): The student profile containing information such as grade level.
    question_number (str): The number of questions in the assignment.
    assignment_type (str): The type of assignment being simplified.

    Returns:
    dict: The simplified assignment with advanced words replaced by simpler alternatives.
    """

    print("Simplifying Vocabulary")
    challenges_description = get_challenges_description(assignment_type, student)
    tools=[
        {
        "type": "function",
        "function": {
            "name": "translate_assignment",
            "description": "translate the given assignment",
            "parameters": {
                "type": "object",
                'properties': { 
                    "challenges": challenges_description,
                    "overall_grade": {
                        "type": "string",
                        "description": "overall grade for the assignment"
                    },
                    "overall_personalized_feedback": {
                        "type": "string",
                        "description": "overall personalized feedback for the assignment"
                    },
                    "overall_engagement_grade": {
                        "type": "string",
                        "description": "overall engagement grade for the assignment"
                    },
                    "student_id": {
                        "type": "string",
                        "description": "student id"
                    },
                    "teacher_id": {
                        "type": "string",
                        "description": "teacher id"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "session id"
                    },
                },
                "required":["challenges","overall_grade","overall_personalized_feedback","student_id","teacher_id","session_id"]
            }
        }
        }
    ]
    # Get the appropriate vocabulary list for the given grade
    spanish_wordlist = spanish_vocab_example(student['grade'])
    prompt = f"""
    You are an AI language model designed to simplify vocabulary in educational materials. 
    Your task is to replace advanced words in the following Spanish assignment with simpler alternatives suitable for grade {student['grade']} students. 
    Ensure the context and meaning of the original assignment remain unchanged. 
    Use the provided vocabulary list to find appropriate simpler words.
    Be sure to keep the exact same number of questions: {question_number}.
        Make sure that the score stays exactly the same as the original assignment.
    Make sure that the feedback retains the same meaning and tone as the original assignment, but is translated into spanish if not already in the spanish language.
    
    Original Assignment:
    {graded_assignment}    
    Vocabulary List:
    {spanish_wordlist}
    """
    messages = [
        {"role": "system", "content": "You are a language simplification assistant."},
        {"role": "user", "content": prompt}
    ]
    
    grades =  make_openai_request_with_tools(messages,tools,0.3)
    return grades

def contextual_and_grammar_agent(graded_assignment, student,question_number, assignment_type):
    """
    This function refines the context and grammar of a Spanish assignment to ensure it is accurate and appropriate for the given grade level.
    It simplifies complex sentences, corrects grammatical errors, and ensures the assignment is contextually accurate for the specified grade.

    Parameters:
    graded_assignment (dict): The graded Spanish assignment in JSON format.
    student (dict): The student profile containing information such as grade level.
    question_number (str): The number of questions in the assignment.
    assignment_type (str): The type of assignment being refined.

    Returns:
    dict: The refined assignment with improved context and grammar.
    """
    print("Refining Context and Grammar")
    challenges_description = get_challenges_description(assignment_type, student)
    tools=[
        {
        "type": "function",
        "function": {
            "name": "translate_assignment",
            "description": "translate the given assignment ",
            "parameters": {
                "type": "object",
                'properties': { 
                    "challenges": challenges_description,
                    "overall_grade": {
                        "type": "string",
                        "description": "overall grade for the assignment"
                    },
                    "overall_personalized_feedback": {
                        "type": "string",
                        "description": "overall personalized feedback for the assignment"
                    },
                    "overall_engagement_grade": {
                        "type": "string",
                        "description": "overall engagement grade for the assignment"
                    },
                    "student_id": {
                        "type": "string",
                        "description": "student id"
                    },
                    "teacher_id": {
                        "type": "string",
                        "description": "teacher id"
                    },
                    "session_id": {
                        "type": "string",
                        "description": "session id"
                    },
                },
                "required":["challenges","overall_grade","overall_personalized_feedback","student_id","teacher_id","session_id"]
            }
        }
        }
    ]
    # Create the prompt
    prompt = f"""
    You are an AI language model designed to refine educational materials. 
    Your task is to ensure the following Spanish assignment is contextually accurate and grammatically correct for grade {student['grade']} students. 
    Simplify complex sentences and ensure the grammar is appropriate for the grade level.
    Be sure to keep the exact same number of questions: {question_number}.
    Make sure that the score stays exactly the same as the original assignment.
    Make sure that the feedback retains the same meaning and tone as the original assignment, but is translated into spanish if not already in the spanish language.

    Simplified Assignment:
    {graded_assignment}
    """
    
    messages = [
        {"role": "system", "content": "You are a contextual and grammar refinement assistant."},
        {"role": "user", "content": prompt}
    ]
    
    # Make the OpenAI request and return the refined assignment
    return make_openai_request_with_tools(messages, tools, temperature=0.5)


def get_challenges_description(assignment_type, student):
    challenges_description = {}
    if student['state_standard'] == "New York State Standard" and 'essay' in assignment_type:
        if student['grade'] in ["4th", "5th"]:
            if "essay" in assignment_type:
                challenges_description = {
                    "type": "object",
                    "properties": {
                        "grade": {
                            "type": "string",
                            "description": "overall grade earned by the student for that question"
                        },
                        "personalized_feedback": {
                            "type": "string",
                            "description": "feedback for the student for that question. Always has to be provided."
                        },
                        "Content and Analysis Grade": {
                            "type": "string",
                            "description": "Grade for content and analysis"
                        },
                        "Command of Evidence Grade": {
                            "type": "string",
                            "description": "Grade for command of evidence"
                        },
                        "Coherence, Organization, and Style Grade": {
                            "type": "string",
                            "description": "Grade for coherence, organization, and style"
                        },
                        "Control of Conventions Grade": {
                            "type": "string",
                            "description": "Grade for control of conventions"
                        }
                    },
                    "required": [
                        "grade",
                        "personalized_feedback",
                        "Content and Analysis Grade",
                        "Command of Evidence Grade",
                        "Coherence, Organization, and Style Grade",
                        "Control of Conventions Grade"
                    ]
                }
            else:
                challenges_description = {
                    "type": "array",
                    "items": {
                    "type": "object",
                    "properties": {
                        "index": {
                            "type": "string",
                            "description": "The question number"
                        },
                        "grade": {
                            "type": "string",
                            "description": "over all grade_earned_by_student for that question"
                        },
                        "Content and Analysis Grade": {
                            "type": "string",
                            "description": "Grade for content and analysis"
                        },
                        "Command of Evidence Grade": {
                            "type": "string",
                            "description": "Grade for command of evidence"
                        },
                        "Coherence, Organization, and Style Grade": {
                            "type": "string",
                            "description": "Grade for coherence, organization, and style"
                        },
                        "Control of Conventions Grade": {
                            "type": "string",
                            "description": "Grade for control of conventions"
                        },
                        "personalized_feedback": {
                            "type": "string",
                            "description": "feedback for the student for that question. Always have to be provided. "
                        },
                    },
                    "required":["grade","personalized_feedback", "Content and Analysis Grade", "Command of Evidence Grade", "Coherence, Organization, and Style Grade", "Control of Conventions Grade"],
                    },
                }
        elif student['grade'] in ["3rd", "6th", "7th", "8th"]: 
            if "essay" in assignment_type:
                challenges_description = {
                    "type": "object",
                    "properties": {
                        "grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question"
                        },
                        "personalized_feedback": {
                            "type": "string",
                            "description": "feedback for the student for that question. Always have to be provided. "
                        },
                    },
                    "required":["grade","personalized_feedback"],
                    }
                    
            else:
                challenges_description = {
                        "type": "array",
                        "items": {
                        "type": "object",
                        "properties": {
                            "index": {
                                "type": "string",
                                "description": "The question number"
                            },
                            "grade": {
                                "type": "string",
                                "description": "grade_earned_by_student for that question"
                            },
                            "personalized_feedback": {
                                "type": "string",
                                "description": "feedback for the student for that question. Always have to be provided. "
                            },
                        },
                        "required":["index","grade","personalized_feedback"],
                        },
                    }
    elif "math" in assignment_type: 
        challenges_description = { 
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "index": {
                            "type": "string",
                            "description": "The question number"
                        },
                        "grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question"
                        },
                        "personalized_feedback": {
                            "type": "string",
                            "description": "edited personalized feedback for the student for that question. Always have to be provided. "
                        },
                        "original_feedback": {
                            "type": "string",
                            "description": "original feedback for the student for that question. Always have to be provided. "
                        },
                        "reviewed": {
                            "type": "boolean",
                            "description": "Question reviewed or not. "
                        },
                    },
                    "required":["index","grade","personalized_feedback","original_feedback","reviewed"],
                    },
                }
    elif assignment_type=="history_critical":
        challenges_description = {
                "type": "array",
                "items": {
                "type": "object",
                "properties": {
                    "index": {
                        "type": "string",
                        "description": "The question number"
                    },
                    "critical_thinking_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Critical Thinking and Analysis"
                    },
                    "standard_understanding_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Understanding of the Standard"
                    },
                    "engagement_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Engagement with the Topic and Interest"
                    },
                    "evidence_support_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Evidence and Support"
                    },
                    "organization_clarity_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Organization and Clarity"
                    },
                    "personalized_feedback": {
                        "type": "string",
                        "description": "feedback for the student for that question. Always have to be provided."
                    },
                },
                "required":["index","critical_thinking_grade","standard_understanding_grade","engagement_grade","evidence_support_grade","organization_clarity_grade","personalized_feedback"],
                },
            }
    elif assignment_type=="history_fact":
        challenges_description = {
                "type": "array",
                "items": {
                "type": "object",
                "properties": {
                    "index": {
                        "type": "string",
                        "description": "The question number"
                    },
                    "correctness_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Correctness of Answer"
                    },
                    "understanding_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for their topic understanding"
                    },
                    "engagement_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for their engagement"
                    },
                    "personalized_feedback": {
                        "type": "string",
                        "description": "feedback for the student for that question. Always have to be provided. "
                    },
                },
                "required":["index","correctness_grade","understanding_grade","engagement_grade","grade","personalized_feedback"],
                },
            }
    elif assignment_type=="history_vocab":
        challenges_description = {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "index": {
                            "type": "string",
                            "description": "The question number"
                        },
                        "correctness_grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question for Correctness"
                        },
                        "topic_understanding_grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question for Understanding of the Topic"
                        },
                        "concept_engagement_grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question for Engagement with Concepts"
                        },
                        "personalized_feedback": {
                            "type": "string",
                            "description": "feedback for the student for that question. Always have to be provided."
                        }
                    },
                    "required": ["index", "correctness_grade", "topic_understanding_grade", "concept_engagement_grade","personalized_feedback"]
                },
                "description": "A list of json objects with question number, grade and personalized feedback for each rubric"
            }
    elif assignment_type=="history_essay":
        challenges_description = {
                "type": "object",
                "properties": {
                    "prompt_answering_grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question for Answering the Prompt"
                        },
                    "factual_accuracy_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Factual Accuracy"
                    },
                    "writing_quality_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Writing Quality(Grammar, Spelling, Vocabulary, Depth)"
                    },
                    "topic_engagement_grade": {
                        "type": "string",
                        "description": "grade_earned_by_student for that question for Engagement with Topic and Interest"
                    },
                    "personalized_feedback": {
                        "type": "string",
                        "description": "feedback for the student for that question. Always have to be provided. "
                    },
                },
                "required":["prompt_answering_grade","factual_accuracy_grade","writing_quality_grade","topic_engagement_grade","personalized_feedback"],
            }
    elif assignment_type in ["arg_essay", "inf_essay"]:
        challenges_description = {
                "type": "object",
                "properties": {
                    "prompt_answering_grade": {
                            "type": "string",
                            "description": "grade_earned_by_student for that question for Answering the Prompt"
                    },
                    "engagement_grade": {
                    "type": "string",
                    "description": "Grade for student engagement"
                    },
                    "factual_grade": {
                    "type": "string",
                    "description": "Grade for factual accuracy"
                    },
                    "grade": {
                    "type": "string",
                    "description": "Overall grade for the question"
                    },
                    "grammar_grade": {
                    "type": "string",
                    "description": "Grade for grammar"
                    },
                    "personalized_feedback": {
                    "type": "string",
                    "description": "Personalized feedback for the student"
                    },
                    "students_answer": {
                    "type": "string",
                    "description": "The student's answer"
                    },
                    "topic_grade": {
                    "type": "string",
                    "description": "Grade for understanding of the topic"
                    },
                    "vocabulary_grade": {
                    "type": "string",
                    "description": "Grade for vocabulary usage"
                    }
                }
            }
    else: 
        challenges_description = {
            "type": "array",
            "items": {
            "type": "object",
            "properties": {
                "index": {
                "type": "string",
                "description": "The question number"
                },
                "answerKeys": {
                "type": "string",
                "description": "The correct answer or explanation for the question"
                },
                "description": {
                "type": "string",
                "description": "Description of the question"
                },
                "engagement_grade": {
                "type": "string",
                "description": "Grade for student engagement"
                },
                "factual_grade": {
                "type": "string",
                "description": "Grade for factual accuracy"
                },
                "grade": {
                "type": "string",
                "description": "Overall grade for the question"
                },
                "grammar_grade": {
                "type": "string",
                "description": "Grade for grammar"
                },
                "personalized_feedback": {
                "type": "string",
                "description": "Personalized feedback for the student"
                },
                "proof_of_work": {
                "type": "boolean",
                "description": "Proof of work provided or not"
                },
                "reasoning": {
                "type": "string",
                "description": "Reasoning behind the student's answer"
                },
                "students_answer": {
                "type": "string",
                "description": "The student's answer"
                },
                "task": {
                "type": "string",
                "description": "The task or question given to the student"
                },
                "topic_grade": {
                "type": "string",
                "description": "Grade for understanding of the topic"
                },
                "vocabulary_grade": {
                "type": "string",
                "description": "Grade for vocabulary usage"
                }
            },
            "required": ["index", "answerKeys", "description", "engagement_grade", "factual_grade", "grade", "grammar_grade", "personalized_feedback", "proof_of_work", "reasoning", "students_answer", "task", "topic_grade", "vocabulary_grade"]
            }
        }
        
    return challenges_description
        
