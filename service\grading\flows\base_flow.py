# base_flow.py
from langgraph.graph import Graph
from typing import Dict, Any
from llm_service import LLMService

class BaseGradingFlow:
    def __init__(self):
        self.llm_service = LLMService()
        self.workflow = Graph()
    
    def build_workflow(self):
        """To be implemented by child classes"""
        raise NotImplementedError
    
    def initialize_state(self, input_data: Dict[str, Any]):
        """Common state initialization"""
        return {
            **input_data,
            "errors": [],
            "warnings": [],
            "metadata": {}
        }
    
    def common_error_handler(self, state: Dict):
        """Shared error handling"""
        if state.get("errors"):
            # Implement common error processing
            pass
        return state