import unittest
from functions_framework import create_app
import json


class TestGradeAssignmentEndpoint(unittest.TestCase):
    def setUp(self):
        # Setup the Flask application as a test client
        self.app = create_app('grade_assignment', source='main.py').test_client()

    def test_grade_assignment(self):
        # Prepare the mock data to simulate a request
        request_data = {
            'challenges': json.dumps([
                {'answerKeys': '{"answer": "4x + 4"}',
                 'description': 'The first puzzle on the map shows a trinomial equation representing the distance in miles to the first landmark.',
                 'index': '1', 'scenario': '<PERSON> finds a mysterious map in his attic, pointing to the lost treasure of Numeria. The map is filled with mathematical puzzles that need solving to reveal the path.',
                 'students_answer': '5x - 7', 'task': 'Use long division to divide the trinomial $4x^2 - 16x - 20$ by $x - 2$ to find the distance to the first landmark.',
                 'title': 'The Mysterious Map'},
                {'answerKeys': '{"answer": "3x + 6"}',
                 'description': 'The bridge has a sequence of stones, each with a trinomial equation. <PERSON> must solve the equation on the first stone to know how many steps to take.',
                 'index': '2', 'scenario': 'To reach the next clue, <PERSON> must cross the Bridge of Challenges, where each step is represented by a trinomial equation.',
                 'students_answer': '6x + 8', 'task': 'Solve the trinomial equation $3x^2 + 9x + 6$ by $x + 1$ using long division to find out how many steps John should take to safely cross the bridge.',
                 'title': 'The Bridge of Challenges'},
                {'answerKeys': '{"answer": "5x - 5"}',
                 'description': 'The Guardian presents a trinomial equation as a riddle, saying, "Solve this, and the path shall open."',
                 'index': '3',
                 'scenario': 'At the end of the bridge, John encounters the Guardian of the Path, who challenges him with a riddle involving a trinomial equation to pass.',
                 'students_answer': '5x - 5',
                 'task': "Use long division to divide the trinomial $5x^2 + 20x + 15$ by $x + 3$ to solve the Guardian's riddle and continue on the path.",
                 'title': "The Guardian's Riddle"}
            ]),
            'state_standard': 'State Curriculum',
            'topic': 'STEM',
            'grade': '12',
            'name': 'John Doe',
            'student_id': '1',
            'teacher_id': '1',
            'session_id': '1'
        }

        # Send POST request to the grade_assignment function
        response = self.app.post('/grade', json=request_data, content_type='application/json')

        # Check response status code and the content of the response
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.data.decode('utf-8'))

        self.assertIn('Challenges', response_data)
        self.assertTrue('overall_grade' in response_data)
        self.assertIsInstance(response_data['Challenges'], list)
        self.assertGreaterEqual(response_data['overall_grade'], 0)


if __name__ == '__main__':
    unittest.main()
