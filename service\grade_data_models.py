from pydantic import BaseModel, Field, Extra
from typing import Dict, Optional, List, Literal, Union,Annotated
from aisdk.data_models.pipeline_context import Requests, PipelineContext
import random

def dict_update_reducer(a: dict, b: dict) -> dict:
    a = dict(a) 
    a.update(b)
    return a

class HomeworkAnswer(BaseModel):
    """Represents a student's answer to a specific homework question."""
    question_number: Union[str, int] = Field(..., description="The number or unique identifier for the question.")
    students_answer: str = Field(..., description="The student's answer for the given question.")
    image: Optional[str] = Field(default=None, description="Base64-encoded string of an image provided as an answer (for visual questions, image tasks, etc.).")

class BoundingBox(BaseModel):
    """Describes a bounding box with coordinates (for visual questions, image tasks, etc.)."""
    x1: float = Field(..., description="X-coordinate of the top-left corner.")
    y1: float = Field(..., description="Y-coordinate of the top-left corner.")
    x2: float = Field(..., description="X-coordinate of the bottom-right corner.")
    y2: float = Field(..., description="Y-coordinate of the bottom-right corner.")

class GradeRequest(Requests):
    """Represents the parameters and data required for grading an assignment."""
    answers: List[HomeworkAnswer] = Field(..., description="List of answers, each corresponding to a homework question.")
    state_standard: str = Field(..., description="The state standard relevant to the assignment.")
    assignment_type: str = Field(..., description="The type of the assignment (e.g., quiz, test, homework).")
    language: str = Field(default="english", description="The language in which the assignment and answers are provided.")
    assignment_name: str = Field(default="Test_assignment", description="The descriptive name or title of the assignment.")
class GradePipelineContext(PipelineContext):
    """Represents the full context and state of the grading pipeline for an assignment submission."""
    request: GradeRequest = Field(..., description="The request object containing all grading parameters and data.")
    adjustment_attempts: int = Field(default=0, description="Number of adjustment attempts (e.g., for regrading or recalibration).")
    request_id: str = Field(default='Test_request_id', description="Unique identifier for this grading request.")
    interest_information: str = Field(default="", description="Any student-specific interest information relevant to personalized grading or feedback.")
    previous_response: Optional[Dict] = Field(default_factory=dict, description="Stores previous responses or states, useful for tracking pipeline history or iterative grading.")
    overall_grade: float = Field(default=0, description="The final grade for the entire assignment.")
    overall_personalized_feedback: str = Field(default="", description="The final personalized feedback for the entire assignment.")