import ast
def simplify_challenges(assignment, question_number):
    
    """
    Simplifies a given assignment by extracting and returning only the relevant question 
    based on the provided question number.

    This function processes the assignment, which may be a string (JSON-like format) or a list of dictionaries.
    If the assignment contains multiple questions, it filters out only the question matching the given question_number.

    Parameters:
    - assignment (str or list): The assignment data, which can be a JSON-like string or a list of dictionaries.
    - question_number (str or int): The specific question index to extract.

    Returns:
    - list: A list containing the extracted question dictionary. If the assignment is already a list, 
      it maintains the same structure.
    """    
    if type (assignment)==str:
        assignment=ast.literal_eval(assignment)
    print("simplify_challenges used")

    for question in assignment:
        if "index" in question and str(question["index"]) == str(question_number):
            assignment=question
    print("ASSIGNMENT:",assignment)
    if type(assignment)==list:
        return assignment
    else:
        return [assignment]

