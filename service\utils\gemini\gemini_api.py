
from openai import OpenAI
import json
from service.utils.validate_response import validate_output_structure
from tenacity import retry, stop_after_attempt, wait_fixed

@retry(stop=stop_after_attempt(3),wait=wait_fixed(2))
def make_gemini_vision_request(message,tools,temperature,images,description=[]):
    client = OpenAI(
    api_key="AIzaSyBgCh-3mnmQHsvmZwXQT_7BxeixliGOVxY",
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)
    print("USING VISION WITH GEMINI")
    vision_request = {
        "role": "user",
        "content": [],
    }

    for index, image in enumerate(images):
        base64_image = image
        content_entry = []

        if description and index < len(description): 
            content_entry.append({
                "type": "text",
                "text": f"Here is the description of the image-{index} containing the students_answer: {description[index]}"
            })
        else:
            content_entry.append({
                "type": "text",
                "text": f"Describe the image given in great detail"
            })
        content_entry.append({
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{base64_image}",
                "detail": "high"
            }
        })
        vision_request["content"].extend(content_entry)

    message.append(vision_request)
    #print("message used for GEMINI:",message)
    try:
        response = client.chat.completions.create(
            model="gemini-2.0-flash",
            temperature=temperature,
            messages=message,
            tools=tools,
            tool_choice="required"
        )
    except Exception as e:
        print("ERROR IN GEMINI VISION REQUEST:",e)
        raise e
    data=response.choices[0].message.tool_calls[0].function.arguments
    print("GEMINI RESPONSE:",data)
    if 'tool_uses' in data:
        data = data['tool_uses'][0]['parameters']
    print("response from GEMINI:",data)
    data=json.loads(data)
    verify_output=validate_output_structure(data,tools)
    print(type(data))
    return data