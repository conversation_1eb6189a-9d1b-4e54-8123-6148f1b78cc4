import ast
from service.utils.gpt_call import make_openai_request_with_tools
from service.vision.vision import make_openai_vision_request
from service.logging import log_function_io
from service.vision.simplify_challenges import simplify_challenges
import globals
import os
from service.langfuse_client import langfuse

@log_function_io
def prompt_for_step_evaluation(assignment, assignment_type,context='', use_vision=False, images=[], question_number='1'):
    """
    Evaluates a student's assignment to determine if it demonstrates original work and problem-solving steps.
    
    Purpose:
    - This function analyzes the student's assignment to check if it includes evidence of problem-solving steps, 
      intermediate calculations, or reasoning. It also determines whether the work is original or copied.

    Parameters:
    - assignment (str or dict): The student's assignment, either as a string (to be converted) or a dictionary.
    - assignment_type (str): The type of assignment (e.g., math, history, or essay).
    - context (str, optional): Additional context for the assignment.
    - use_vision (bool, optional): Whether to use image analysis for evaluation.
    - images (list, optional): List of image files to analyze if `use_vision` is True.
    - question_number (str, optional): The question number to evaluate.

    Returns:
    - dict: Updated assignment with evaluation results, including proof of work and reasoning.
    """

    if type(assignment) == str:
        assignment = ast.literal_eval(assignment)

    math_instructions = ""
    history_instructions = ""
    other_instructions = ""
    nb_of_questions = globals.assignment_length
    assignment_context = ""

    if "math" in assignment_type:
        math_instructions += """\n
        The assignment is a math task. Your goal is to check if the student's response includes any steps, calculations, or intermediate work leading up to the final answer. If the response shows detailed calculations, written steps, or diagrams that demonstrate problem-solving, it is more likely the student did the work themselves.
        Additionally, you are required to calculate the student's engagement grade, which reflects how actively the student worked on the problem. Engagement is evaluated based on the following criteria:

        - If the student's answer includes clear and complete problem-solving steps leading to the final solution, assign an engagement grade of 90-100%.
        - If the student's answer includes some problem-solving steps but lacks thoroughness or skips key parts, assign an engagement grade of 70-89%.
        - If the student's answer provides minimal problem-solving steps, or only partial evidence of the methodology, assign an engagement grade of 50-69%.
        - If the student's answer only includes the final answer with no visible steps or methodology, assign an engagement grade below 50%.
        """

        if use_vision:
            math_instructions += """\n
            The input contains an image file (`students_answer_image`) showing the student's answer. Analyze the image and identify steps or calculations.
            
            Task:
            - Analyze the `students_answer_image` for intermediate steps, written explanations, or diagrams.
            - Return a Boolean value of `True` if there is evidence of problem-solving steps.
            - Return a Boolean value of `False` if only the final answer is provided.
            """
        else:
            math_instructions += """\n
            The input contains a text-based answer (`students_answer`). Analyze the text to identify any calculations, explanations, or annotations.
            
            Task:
            - Review the `students_answer` to determine if it shows evidence of steps.
            - Return a Boolean value of `True` if there is proof of problem-solving.
            - Return a Boolean value of `False` if only the final answer is given.
            """

    elif "history" in assignment_type:
        history_instructions += """\n
        The assignment is a history task. Your goal is to check if the student's response shows an understanding of historical events, concepts, or analysis rather than being a direct copy of factual information. Look for explanations, interpretations, or connections made by the student to the historical context.
        """
        if use_vision:
            history_instructions += """\n
            The input contains an image file (`students_answer_image`) showing the student's answer. Analyze the image and identify signs of historical analysis, reasoning, or understanding.
            
            Task:
            - Analyze the `students_answer_image` for signs of understanding, such as explanations, reasoning, or connecting historical events.
            - Return a Boolean value of `True` if the response demonstrates the student's own analysis.
            - Return a Boolean value of `False` if the answer appears to be a direct copy of historical facts without analysis.
            """
        else:
            history_instructions += """\n
            The input contains a text-based answer (`students_answer`). Analyze the text to identify signs of historical analysis, reasoning, or understanding.
            
            Task:
            - Analyze the `students_answer` for signs of understanding, such as explanations, reasoning, or connecting historical events.
            - Return a Boolean value of `True` if the response demonstrates the student's own analysis.
            - Return a Boolean value of `False` if the answer appears to be a direct copy of historical facts without analysis.
            """
    else:
        other_instructions += """\n
        The assignment is an English task. Your goal is to check if the student's response reflects their understanding of the passage or prompt, rather than being a direct copy from external sources. This could include paraphrasing, critical analysis, or references to specific parts of the passage.
        """
        if use_vision:
            other_instructions += """\n
            The input contains an image file (`students_answer_image`) showing the student's answer. Analyze the image and identify signs of original work, paraphrasing, or references to the passage.
            
            Task:
            - Analyze the `students_answer_image` for signs of original thought, paraphrasing, or interpretation.
            - Return a Boolean value of `True` if there is evidence of original work.
            - Return a Boolean value of `False` if the response appears to be copied without reflection or modification.
            """
        else:
            other_instructions += """\n
            The input contains a text-based answer (`students_answer`). Analyze the text to identify signs of original thought, paraphrasing, or references to the passage.
            
            Task:
            - Evaluate the `students_answer` for signs of original thought, such as paraphrased content, references to the passage, or personal interpretation.
            - Return a Boolean value of `True` if there is evidence of original work.
            - Return a Boolean value of `False` if the response appears to be copied without reflection or modification.
            """

    if use_vision:
        assignment=simplify_challenges(assignment,question_number) 
   
    if context:
        assignment_context += f"\nThis is the context used for the assignment: {context}"    
        
    tools = [
        {
            "type": "function",
            "function": {
                "name": "assess_methodology",
                "description": "Check whether the student's answer contains methodology or not",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "check_methodology": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "index": {
                                        "type": "string",
                                        "description": "The question number"
                                    },
                                    "contains_steps": {
                                        "type": "boolean",
                                        "description": "'True' if the student's answer contains steps, 'False' otherwise (Case Sensitive)"
                                    },
                                  "reasoning": {
                                        "type": "string",
                                        "description": "Reasoning on why you think whether it contains_steps or not."
                                    },
                                },
                                "required": ["index", "contains_steps","reasoning"],
                            },
                        },
                    },
                    "required": ["check_methodology"]
                }
            }
        }
    ]
    if 'essay' in assignment_type:
        tools = [
            {
            "type": "function",
            "function": {
                "name": "assess_methodology",
                "description": "Check whether the student's answer contains methodology or not",
                "parameters": {
                "type": "object",
                "properties": {
                    "check_methodology": {
                    "type": "object",
                    "properties": {
                        "index": {
                        "type": "string",
                        "description": "The question number"
                        },
                        "contains_steps": {
                        "type": "boolean",
                        "description": "'True' if the student's answer contains steps, 'False' otherwise (Case Sensitive)"
                        },
                        "reasoning": {
                        "type": "string",
                        "description": "Reasoning on why you think whether it contains_steps or not."
                        }
                    },
                    "required": ["index", "contains_steps", "reasoning"]
                    }
                },
                "required": ["check_methodology"]
                }
            }
        }

    ]
    if 'math' in assignment_type:
        tools[0]["function"]["parameters"]["properties"]["check_methodology"]["items"]["properties"]["engagement_grade"] = {
            "type": "string",
            "description": "Engagement grade of the student."
        }
        tools[0]["function"]["parameters"]["properties"]["check_methodology"]["items"]["required"].append("engagement_grade")

    messages = langfuse.get_prompt("step_evaluation", label="latest")
    complete_chat_prompt = messages.compile(
            assignment=assignment,
            nb_of_questions=nb_of_questions,
            assignment_context=assignment_context,
            math_instructions=math_instructions,
            history_instructions=history_instructions,
            other_instructions=other_instructions
        )

    # Make the request based on the use of vision
    if use_vision:
        validity = make_openai_vision_request(complete_chat_prompt, tools, 0.7, images)
    else:
        validity = make_openai_request_with_tools(complete_chat_prompt, tools, 0.7)
    print("validity:",validity)
   
    updated_assignment = process_assignment(assignment, assignment_type, validity)
    print(updated_assignment)
    return assignment

def process_assignment(assignment, assignment_type, validity):
    """
    
    Processes an assignment based on its type and the validity checks.

    Purpose:
    - This function updates the assignment based on the validity check results. 
      It ensures that the proof of work, reasoning, and engagement grade (if applicable) are added to the assignment.

    Parameters:
    - assignment (dict): The assignment data to process.
    - assignment_type (str): The type of assignment (e.g., 'essay', 'math').
    - validity (dict): Dictionary containing validity check results, including methodology and reasoning.

    Returns:
    - dict: The updated assignment with proof_of_work, reasoning, and engagement grade (for math assignments).
  
    """
    try:
        if not isinstance(assignment, dict):
            raise ValueError("Invalid assignment format. Expected a dictionary.")
        if not isinstance(validity, dict):
            raise ValueError("Invalid validity format. Expected a dictionary.")
        if not isinstance(assignment_type, str):
            raise ValueError("Invalid assignment_type format. Expected a string.")

        if 'essay' in assignment_type:
            assignment['proof_of_work'] = validity['check_methodology'].get('contains_steps', None)
            assignment['reasoning'] = validity['check_methodology'].get('reasoning', None)
        else:
            check_methodology = validity.get('check_methodology', [])
            if not isinstance(check_methodology, list):
                raise ValueError("check_methodology should be a list for non-essay types.")

            for question in check_methodology:
                try:
                    index = int(question.get('index', -1))
                    if index == -1:
                        raise ValueError("Invalid or missing index in question.")

                    if 'homework' in assignment:
                        assignment = assignment.get('homework', {})

                    if 'challenges' in assignment:
                        for challenge in assignment['challenges']:
                            if int(challenge.get('index', -1)) == index:
                                challenge['proof_of_work'] = question.get('contains_steps', None)
                                challenge['reasoning'] = question.get('reasoning', None)

                                if 'math' in assignment_type:
                                    challenge['engagement_grade'] = question.get('engagement_grade', None)
                    else:
                        for challenge in assignment:
                            if int(challenge.get('index', -1)) == index:
                                challenge['proof_of_work'] = question.get('contains_steps', None)
                                challenge['reasoning'] = question.get('reasoning', None)

                except Exception as inner_error:
                    print(f"Error processing question with index {question.get('index', 'unknown')}: {inner_error}")

        return assignment

    except Exception as e:
        print(f"An error occurred: {e}")
        return None
