import openai
import json

def image_quality_check_agent(image):
    """
    Evaluates the quality of a given screenshot to determine whether it meets 
    specific quality standards.

    The function sends the image to OpenAI's gpt-4.1-nano model using vision capabilities. 
    It checks the following criteria:
    - The image contains only the student's answer without interface elements.
    - The resolution is sufficient, and text/content is clear and legible.
    - The entire answer is captured with no overlapping or cut-off portions.
    - The image is not blurry and free of visual noise.
    - The zoom level is optimal (not too small or distorted).

    Parameters:
    - image (str): The base64-encoded string of the image.

    Returns:
    - dict: A dictionary containing:
        - 'standard' (bool): True if the image meets all guidelines, False otherwise.
        - 'reason' (str): Explanation of why the image meets or fails to meet the standard.  
    """
  
    prompt="""You are an image quality evaluator designed to assess screenshots based on a given checklist of standards. Your task is to analyze the provided screenshot and determine if it meets the specified quality guidelines. Follow these steps:
            Check if the screenshot contains only the student's answer without any additional interface elements.
            Ensure the resolution is sufficient and the text or content is clear and legible.
            Verify that the screenshot captures the entire answer without any overlapping, cutoff, or partial visibility.
            Confirm that the screenshot is not blurry and has no visual noise.
            Check that the screenshot is taken at an optimal zoom level (not too small or distorted).
            Based on your evaluation, generate a JSON response with the following keys:
            'Standard': (True or False) – True if the image meets all guidelines, False otherwise.
            NOTE: The boolean response value should be either True or False only (case sensitive)
            'reason': Provide a clear reason why the image meets the standards or what specific issues prevent it from meeting the standards.
            For example: { 'Standard': False, 'reason': 'The image is blurry, and part of the student's answer is cut off.' }
            Analyze the image according to these criteria and provide an accurate assessment."""
    tools=[
          {
        "type": "function",
        "function": {
            "name": "image_quality_check",
            "description": "check the quality of the image",
            "parameters": {
                    "type": "object",
                    "properties": {
                      "quality": {
                        "type": "object",
                        "properties": {
                          "standard": {
                            "type": "boolean",
                            "description": "Indicates whether the image meets all the specified quality guidelines. True if it meets all standards; otherwise, False."
                          },
                          "reason": {
                            "type": "string",
                            "description": "A detailed explanation of why the image meets the standards or why it fails to meet them. If 'standard' is False, provide specific issues."
                          }
                        },
                        "required": ["standard", "reason"]
                      }
                    },
                    "required": ["quality"]
                  }
                }
            }
        ]
    message=[
        {"role": "system", "content": prompt},
        {"role": "user", "content": [
        {"type": "text", "text": "Please check whether the image given meets the standard or not."},
        {
          "type": "image_url",
          "image_url": {
            "url": f"data:image/jpeg;base64,{image}",
          },},],}]
    try:
      response = openai.chat.completions.create(
          model="gpt-4.1-nano",
          temperature=0.3,
          messages=message,
          tools=tools,
          tool_choice="required"
      )
      data=response.choices[0].message.tool_calls[0].function.arguments
    except Exception as e:
      print("ERROR IN VISION VALIDATION REQUEST:",e)
      raise e
    #print("GPT RESPONSE:",data)
    if 'tool_uses' in data:
        data = data['tool_uses'][0]['parameters']
    print("response from gpt:",data)
    data=json.loads(data)
    return data['quality']
  
