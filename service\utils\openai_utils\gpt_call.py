# Requires review and clean up
import openai
import json

import ast
from service.utils.validate_response import validate_output_structure
from tenacity import retry, stop_after_attempt, wait_fixed
from service.vision.simplify_challenges import simplify_challenges
from service.vision.vision import make_openai_vision_request
from service.logging import log_function_io
import globals
import concurrent.futures

@log_function_io
def make_openai_request(prompt,student,assignment,context,assignment_type,use_vision,question_number,images,response_length=None,tools=[]):
    from service.utils.supporting_functions import reading_level_example
    print("inside make openai")
    reading_example=reading_level_example(student["reading_level"]) 
    if not tools:
        if 'essay' in assignment_type:
            tools=[
            {
            "type": "function",
            "function": {
                "name": "grade_assignment",
                "description": "Grade the given assignment based on the rubrics given in the prompt",
                "parameters": {
                    "type": "object",
                    'properties': {
                    "graded_response": {
                            "type": "object",
                            "properties": {
                                "grade": {
                                    "type": "string",
                                    "description": "grade_earned_by_student for that question"
                                },
                                "personalized_feedback": {
                                    "type": "string",
                                    "description": "feedback for the student for that question. Always have to be provided. "
                                },
                            },
                            "required":["grade","personalized_feedback"],
                            },
                        },
                    "required":["graded_response"]    
                        }
                    }
                }
            ]
        else:
            tools=[
            {
            "type": "function",
            "function": {
                "name": "grade_assignment",
                "description": "Grade the given assignment based on the rubrics given in the prompt",
                "parameters": {
                    "type": "object",
                    'properties': {
                    "graded_response": {
                            "type": "object",
                            "properties": {
                                "index": {
                                        "type": "string",
                                        "description": "The question number"
                                    },
                                "grade": {
                                    "type": "string",
                                    "description": "grade_earned_by_student for that question"
                                },
                                "personalized_feedback": {
                                    "type": "string",
                                    "description": "feedback for the student for that question. Always have to be provided. "
                                },
                            },
                            "required":["index","grade","personalized_feedback"],
                            },
                        },
                    "required":["graded_response"]    
                        }
                    }
                }
            ]
    # if student["state_standard"] == "New York State Standard" and 'essay' in assignment_type:
    #     if student["grade"] in ["3rd", "6th", "7th", "8th"]:
    #         if "essay" not in assignment_type:
    #             tools = [
    #                 {
    #                     "type": "function",
    #                     "function": {
    #                         "name": "grade_assignment",
    #                         "description": "Grade the given assignment based on the rubrics given in the prompt",
    #                         "parameters": {
    #                             "type": "object",
    #                             'properties': {
    #                             "graded_response": {
    #                                     "type": "array",
    #                                     "items": {
    #                                     "type": "object",
    #                                     "properties": {
    #                                         "index": {
    #                                             "type": "string",
    #                                             "description": "The question number"
    #                                         },
    #                                         "grade": {
    #                                             "type": "string",
    #                                             "description": "grade_earned_by_student for that question"
    #                                         },
    #                                         "personalized_feedback": {
    #                                             "type": "string",
    #                                             "description": "feedback for the student for that question. Always have to be provided. "
    #                                         },
    #                                     },
    #                                     "required":["index","grade","personalized_feedback"],
    #                                     },
    #                                 },
    #                             },
    #                             "required":["graded_response"]    
    #                             }
    #                         }
    #                     }
    #                 ]
    #         else:
    #             tools = [
    #                 {
    #                     "type": "function",
    #                     "function": {
    #                         "name": "grade_assignment",
    #                         "description": "Grade the given assignment based on the rubrics given in the prompt",
    #                         "parameters": {
    #                             "type": "object",
    #                             'properties': {
    #                             "graded_response": {
    #                                     "type": "object",
    #                                     "properties": {
    #                                         "grade": {
    #                                             "type": "string",
    #                                             "description": "grade_earned_by_student for that question"
    #                                         },
    #                                         "personalized_feedback": {
    #                                             "type": "string",
    #                                             "description": "feedback for the student for that question. Always have to be provided. "
    #                                         },
    #                                     },
    #                                     "required":["grade","personalized_feedback"],
    #                                     },
    #                                 },
    #                             "required":["graded_response"]    
    #                             }
    #                         }
    #                     }
    #             ]
    #     elif student["grade"] in ["4th", "5th"]:
    #         if "essay" not in assignment_type:
    #             tools = [
    #                 {
    #                     "type": "function",
    #                     "function": {
    #                         "name": "grade_assignment",
    #                         "description": "Grade the given assignment based on the rubrics given in the prompt",
    #                         "parameters": {
    #                             "type": "object",
    #                             'properties': {
    #                             "graded_response": {
    #                                     "type": "array",
    #                                     "items": {
    #                                     "type": "object",
    #                                     "properties": {
    #                                         "index": {
    #                                             "type": "string",
    #                                             "description": "The question number"
    #                                         },
    #                                         "grade": {
    #                                             "type": "string",
    #                                             "description": "over all grade_earned_by_student for that question"
    #                                         },
    #                                         "Content and Analysis Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for content and analysis"
    #                                         },
    #                                         "Command of Evidence Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for command of evidence"
    #                                         },
    #                                         "Coherence, Organization, and Style Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for coherence, organization, and style"
    #                                         },
    #                                         "Control of Conventions Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for control of conventions"
    #                                         },
    #                                         "personalized_feedback": {
    #                                             "type": "string",
    #                                             "description": "feedback for the student for that question. Always have to be provided. "
    #                                         },
    #                                     },
    #                                     "required":["index","grade","personalized_feedback", "Content and Analysis Grade", "Command of Evidence Grade", "Coherence, Organization, and Style Grade", "Control of Conventions Grade"],
    #                                     },
    #                                 },
    #                                 },
    #                             },
    #                             "required":["graded_response"]    
    #                         }
    #                     }
    #                 ]
    #         else:
    #             tools = [
    #                 {
    #                     "type": "function",
    #                     "function": {
    #                         "name": "grade_assignment",
    #                         "description": "Grade the given assignment based on the rubrics given in the prompt",
    #                         "parameters": {
    #                             "type": "object",
    #                             "properties": {
    #                                 "graded_response": {
    #                                     "type": "object",
    #                                     "properties": {
    #                                         "grade": {
    #                                             "type": "string",
    #                                             "description": "overall grade earned by the student for that question"
    #                                         },
    #                                         "personalized_feedback": {
    #                                             "type": "string",
    #                                             "description": "feedback for the student for that question. Always has to be provided."
    #                                         },
    #                                         "Content and Analysis Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for content and analysis"
    #                                         },
    #                                         "Command of Evidence Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for command of evidence"
    #                                         },
    #                                         "Coherence, Organization, and Style Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for coherence, organization, and style"
    #                                         },
    #                                         "Control of Conventions Grade": {
    #                                             "type": "string",
    #                                             "description": "Grade for control of conventions"
    #                                         }
    #                                     },
    #                                     "required": [
    #                                         "grade",
    #                                         "personalized_feedback",
    #                                         "Content and Analysis Grade",
    #                                         "Command of Evidence Grade",
    #                                         "Coherence, Organization, and Style Grade",
    #                                         "Control of Conventions Grade"
    #                                     ]
    #                                 }
    #                             },
    #                             "required": ["graded_response"]
    #                         }
    #                     }
    #                 }
    #             ]
    #print("USNIG GPT")
    if use_vision and 'essay' not in assignment_type:
        assignment=simplify_challenges(assignment,question_number)
    if context:
        user_prompt=f"Here is the information about the student:{student}. \n\n This is the assignments list of JSON objects where each json object is a question: {assignment}. This is the passage that was used in the assignment: {context}"
    else:
        user_prompt=f"Here is the information about the student:{student}. \n\n This is the assignments list of JSON objects where each json object is a question: {assignment}."
    # if response_length:
    #     user_prompt +=f"The number of questions in the assignment are {response_length}"
    # else:
    #     user_prompt += f". The number of questions in the assignment are {globals.assignment_length}"
    messages=[
        {"role": "system", "content": prompt},
        {"role": "system", "content": f"Be sure to match the student's reading level and grade level when providing feedback. Here is an example text that matches the student's reading level: {reading_example}. Make sure to always provide personalized feedback for the stuent for each question. The minimum grade you can assign is 1 so if an answer is completely incorrect or even empty you will give it a grade of 1 and provide motivational feedback."},
        {"role": "user", "content": f"{user_prompt}"},
    ]
    if use_vision:
        grades=make_openai_vision_request(messages,tools,0.3,images)
    else:
        grades=make_openai_request_with_tools(messages,tools,0.3,'gpt-4o',response_length)
    #grades=json.loads(grades)
    if 'index' in assignment and 'index' in grades:
        index_from_assignment=assignment['index']
        index_from_grade=grades['index']
        if int(index_from_assignment)!=int(index_from_grade):
            print("INDEX MISMATCH FOUND")
            print("INDEX IN INPUT:",index_from_assignment,"INDEX IN RESPONSE:",index_from_grade)
            grades['index']=index_from_assignment
    elif 'index' in assignment:
        grades['index']=assignment['index']
    print(grades)
    if 'graded_response' in grades:
        return grades['graded_response']
    else:
        return grades


@log_function_io
def merged_assignments_summary(merged_assignment, student):
    from service.utils.supporting_functions import reading_level_example
    # Precompute anything that is the same for all assignments
    reading_example = reading_level_example(student["reading_level"])

    def process_single_assignment(assignment, student, reading_example):
        """
        This helper handles building the prompt, calling OpenAI, and
        returning the summarized response for a single assignment.
        """
        prompt = """
            Please review the following list of feedback provided to the student.
            Simplify the feedback by summarizing the key points and removing any repetitive or redundant information.
            Ensure that the final list is clear, concise, and easy to understand.
        """
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "summarize_feedback",
                    "description": "Summarize the feedback for the student",
                    "parameters": {
                        "type": "object",
                        'properties': {
                            "summarized_response": {
                                "type": "object",
                                "properties": {
                                    "index": {
                                        "type": "string",
                                        "description": "The question number"
                                    },
                                    "personalized_feedback": {
                                        "type": "string",
                                        "description": "feedback for the student for that question. Always have to be provided."
                                    },
                                },
                                "required": ["index", "personalized_feedback"],
                            },
                        },
                        "required": ["summarized_response"]
                    }
                }
            }
        ]

        messages = [
            {"role": "system", "content": prompt},
            {
                "role": "system",
                "content": (
                    f"Be sure to match the student's reading level and grade level when providing feedback. "
                    f"Here is an example text that matches the student's reading level: {reading_example}."
                )
            },
            {
                "role": "user",
                "content": (
                    f"This is the assignment you will be processing: {assignment}. "
                    f"Here is the information about the student that will be used to make personalized feedback: {student}"
                ),
            },
        ]

        # Call your OpenAI function (or any API function) here
        data = make_openai_request_with_tools(messages, tools, 0.3)
        response = data['summarized_response']

        # Handle index mismatch
        if 'index' in assignment and 'index' in response:
            index_from_assignment = assignment['index']
            index_from_grade = response['index']
            if int(index_from_assignment) != int(index_from_grade):
                print("INDEX MISMATCH FOUND")
                print("INDEX IN INPUT:", index_from_assignment, "INDEX IN RESPONSE:", index_from_grade)
                response['index'] = index_from_assignment
        else:
            # Ensure we always set an index even if the model didn't return one
            response['index'] = assignment.get('index', "0")

        return response

    # We'll store each question's summarized feedback here
    temp = []

    # --- MULTI-THREADING SECTION ---
    with concurrent.futures.ThreadPoolExecutor() as executor:
        # Map each assignment to a future
        future_to_assignment = {
            executor.submit(process_single_assignment, assignment, student, reading_example): assignment
            for assignment in merged_assignment
        }

        # As each future completes, we collect the result
        for future in concurrent.futures.as_completed(future_to_assignment):
            assignment = future_to_assignment[future]
            try:
                response = future.result()
                temp.append(response)
            except Exception as exc:
                print(f"Error processing assignment {assignment}: {exc}")
    # --- END OF MULTI-THREADING SECTION ---

    # Merge the feedback back into merged_assignment
    for assignment in merged_assignment:
        index = assignment.get('index')
        for item in temp:
            if str(index) == str(item['index']):
                assignment['personalized_feedback'] = item['personalized_feedback']
    return merged_assignment

@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
@log_function_io
def make_openai_request_with_tools(message, tools,temperature=0.5, model="gpt-4o",response_length=None):
    print("messages used:",message)
    print("TOOLS USED:",tools)
    #print("length of response=",response_length)
    #print("MODEL:",model)
    try:
        print("Making openai request")
        response = openai.chat.completions.create(
            model=model,
            temperature=temperature,
            messages=message,
            tools=tools,
            tool_choice="required"
        )
        #print("response",response)
    except Exception as e:
        print("Error in openai tool call request",e)
        raise e
    try:
        data=response.choices[0].message.tool_calls[0].function.arguments
        #print("GPT RESPONSE:",data)
        if 'tool_uses' in data:
            data = data['tool_uses'][0]['parameters']
        print("response from gpt:",data)
        if isinstance(data, dict):
            pass
        else:
            data=json.loads(data)
        verify_output=validate_output_structure(data,tools,response_length)
        return data
    except Exception as e:
        print("Error in openai response validation",e)
        raise e

@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
def make_simple_openai_request(messages,temperature=0.5, model="gpt-4o"):
    try:
        response = openai.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature
        )
        print("response",response)
        return response.choices[0].message.content
    except Exception as e:
        print("Error in openai request",e)
        raise e