from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger
from service.langfuse_client import langfuse
from service.utils.supporting_functions import reading_level_example,extract_question_text,generate_question_json,calculate_grade_from_questions
import copy
from concurrent.futures import ThreadPoolExecutor, as_completed

def grade_via_rubrics(assignment: dict,student_profile:dict, context:str, assignment_type:str,dyslexia:bool,old_assignment:dict) -> dict:
    """
    """
    logger.info(f"Starting grade_via_rubrics with assignment: {assignment}")
    try:
        challenges=assignment.challenges
        with ThreadPoolExecutor() as executor:
            futures = {}
            for index, question in enumerate(challenges):

                rubrics = question.rubrics.copy()
                rubrics_questions=extract_question_text(rubrics)

                logger.debug(f"Parsing assignment: {question}")
                if old_assignment:
                    logger.info(f"Old assignment found: {old_assignment}")
                    messages = langfuse.get_prompt("previous_grading_response_template", label="latest")
                    previous_assignment_instructions = messages.compile(
                        old_assignment=old_assignment
                    )
                    logger.info(f"Previous grading response prompt fetched")
                if context:
                    context= f"This is the passage for the assignment: {context}"
                if dyslexia:
                    dyslexia_instructions = "\n\nIMPORTANT: The student has dyslexia. While grading, you MUST NOT penalize for spelling errors at all. Do not consider spelling mistakes when assigning a grade or providing feedback."
                reading_example=reading_level_example(student_profile.reading_level)
                logger.debug("Creating system and user messages for grading via rubrics")
                messages = langfuse.get_prompt("grade_via_rubrics_template", label="latest")
                complete_chat_prompt = messages.compile(
                    assignment=question,
                    student=student_profile,
                    context_based_instructions=context,
                    reading_example=reading_example,
                    dyslexia_instructions=dyslexia_instructions,
                    old_assignment_instructions=previous_assignment_instructions
                )

                tools = generate_question_json(rubrics_questions)


                future  = make_openai_langfuse(complete_chat_prompt, tools)
                futures[future] = index
                logger.info(f"Response from grading via rubrics: {future }")

        for future in as_completed(futures):
            index = futures[future]
            try:
                response = future.result()
                print("response:", response)
                grade = calculate_grade_from_questions(response,5)
                print("grade:", grade)
                grade = round(float(grade), 2)
                temp=response.copy()
                temp.pop('personalized_feedback','')
                temp.pop('index','')
                temp.pop('grade','')
                challenges[index]['rubrics_responses']=temp
                challenges[index]['grade_percentage']=(grade/5)*100
            except Exception as e:
                print(f"Error processing non-essay grading on question {index}: {e}")
        assignment.challenges=challenges
        return assignment
    except Exception as e:
        logger.error(f"Error in grade_via_rubrics: {e}")
        raise


# assignment={
#   "challenges": [
#     {
#       "answerKeys": "Alex feels free and happy when swimming, like flying.",
#       "description": "Think about what 'flying underwater' tells us about how Alex feels when he swims.",
#       "index": "1",
#       "scenario": "John finds a mysterious map in his attic, pointing to the lost treasure of Numeria. The map is filled with mathematical puzzles that need solving to reveal the path.",
#       "students_answer": "Alex feels free and happy when swimming, like flying.",
#       "task": "What does 'flying underwater' mean about how Alex feels when he swims?",
#       "title": "The Mysterious Map"
#     }
#   ],
#   "state_standard": "California Common Core",
#   "topic": "5-ESS1-1 Support an argument that the apparent brightness of the sun and stars is due to their relative distances from Earth.",
#   "grade": "5th",
#   "reading_level": "5th",
#   "assignment_type": "reading_comp_gen"
# }

# student_profile={
#         "grade": "5th",
#         "reading_level": "5th",
#         "first_name": "Alex",
#         "last_name": "Smith",
#         "interest": "swimming",
#         "working_style": "Expressive",
#         "strengths": "Honesty, Compassion, Creativity"
#     }

# context="This is the passage for the assignment: Alex loves to swim. He feels free and happy when he swims. It's like flying underwater."   
# assignment_type="reading_comp_gen"
# dyslexia=False
# old_assignment=None

# grade_via_rubrics(assignment,student_profile, context, assignment_type,dyslexia,old_assignment)